# ===================================================================
#  檔案名稱: manage_system.py
#  版    本: v1.0 - 最終版
#  功    能: 系統級別的管理工具，例如自動維護處理器清單。
# ===================================================================
import pandas as pd
import importlib
from pathlib import Path
import sys
import datetime

def update_manifest():
    """
    自動掃描系統，創建或更新 handler_manifest.xlsx 檔案。
    """
    print("--- 開始掃描系統並更新處理器清單 (handler_manifest.xlsx) ---")
    base_dir = Path(".")
    handlers_dir = base_dir / "handlers"
    templates_dir = base_dir / "form_input" / "templates"
    config_dir = base_dir / "form_input" / "config"
    manifest_path = config_dir / "handler_manifest.xlsx"

    # 1. 掃描 handlers 資料夾，找到所有處理器
    all_handlers = {}
    for handler_path in handlers_dir.glob("*_handler.py"):
        handler_name = handler_path.stem
        try:
            handler_module = importlib.import_module(f"handlers.{handler_name}")
            description = handler_module.__doc__.strip() if handler_module.__doc__ else "尚未提供功能說明。"
            all_handlers[handler_name] = {'description': description, 'forms': []}
        except ImportError:
            print(f"警告：無法導入處理器 '{handler_name}'，已跳過。")

    print(f"-> 發現 {len(all_handlers)} 個處理器插件。")

    # 2. 掃描 templates 資料夾，找到誰在使用哪個處理器
    for layout_path in templates_dir.glob("*_layout.xlsx"):
        try:
            df = pd.read_excel(layout_path, sheet_name='LayoutSettings')
            config = pd.Series(df.Value.values, index=df.Key).to_dict()
            handler_name = config.get('handler_module')
            if handler_name in all_handlers:
                standardized_base_name = layout_path.stem.replace('_layout', '')
                all_handlers[handler_name]['forms'].append(standardized_base_name)
        except Exception:
            print(f"警告：讀取版面設定檔 '{layout_path.name}' 失敗，已跳過。")

    # 3. 準備生成 manifest 的數據
    manifest_data = []
    for name, data in all_handlers.items():
        manifest_data.append({
            'Handler Module': name,
            '功能說明': data['description'],
            '對應使用表單': ", ".join(sorted(data['forms'])) if data['forms'] else "尚無表單使用"
        })
    
    new_manifest_df = pd.DataFrame(manifest_data)

    # 4. 更新或創建 manifest 檔案
    if manifest_path.exists():
        print("-> 發現現有的 manifest 檔案，正在進行更新...")
        old_manifest_df = pd.read_excel(manifest_path).set_index('Handler Module')
        new_manifest_df = new_manifest_df.set_index('Handler Module')
        final_df = new_manifest_df.combine_first(old_manifest_df).reset_index()
    else:
        print("-> 未發現現有的 manifest 檔案，將創建新檔...")
        final_df = new_manifest_df

    final_df['最後更新日期'] = datetime.date.today().strftime("%Y-%m-%d")
    final_df = final_df.reindex(columns=['Handler Module', '功能說明', '對應使用表單', '最後更新日期', '備註'])
    
    final_df.to_excel(manifest_path, index=False)
    print(f"--- 處理器清單已成功更新至 '{manifest_path}' ---")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "update-manifest":
        update_manifest()
    else:
        print("用法: python manage_system.py <命令>")
        print("可用命令:")
        print("  update-manifest   - 自動掃描系統並更新處理器清單 (handler_manifest.xlsx)")