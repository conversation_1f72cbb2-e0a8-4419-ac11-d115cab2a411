import pdfplumber
import re
import pandas as pd
import os
from pathlib import Path
import json
from typing import Dict, List, Any, Optional
# 用於寫入 Excel 檔案，可能需要安裝: pip install openpyxl
import openpyxl
from itertools import groupby
from operator import itemgetter

# 全形轉半形的轉換表
FULL_TO_HALF = str.maketrans(
    "０１２３４５６７８９ＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚ：－（）",
    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz:-()"
)

# --- 常數定義 ---

# 土地標示部欄位解析順序
ORDERED_LAND_FIELDS = [
    "登記日期", "登記原因", "地    目", "等    則", "面    積",
    "使用分區", "使用地類別", "公告土地現值", "地上建物建號", "其他登記事項"
]

# 所有權部欄位解析順序
ORDERED_OWNER_FIELDS = [
    "登記日期", "登記原因", "原因發生日期", "所有權人", "統一編號", "出生日期", "住    址",
    "權利範圍", "權狀字號", "當期申報地價", "前次移轉現值或原規定地價", "歷次取得權利範圍", "其他登記事項"
]

# 所有權部最終 JSON 輸出順序 (包含管理者)
FINAL_OWNER_KEYS = [
    "登記次序", "登記日期", "登記原因", "原因發生日期", "所有權人", "統一編號", "出生日期", "住址",
    "管理者", "管理者統一編號", "管理者住址",
    "權利範圍", "權狀字號", "當期申報地價", "前次移轉現值或原規定地價", "歷次取得權利範圍", "其他登記事項"
]

# 最終 CSV 輸出欄位順序
CSV_COLUMN_ORDER = [
    # 頂層資訊 (依 JSON 結構)
    "地段", "地號", "資料查詢時間",
    # 標示部欄位 (加上前綴以區分)
    "標示部_登記日期", "標示部_登記原因", "標示部_地目", "標示部_等則", "標示部_面積",
    "標示部_使用分區", "標示部_使用地類別", "標示部_公告土地現值", "標示部_地上建物建號", "標示部_其他登記事項",
    # 所有權部欄位 (加上前綴以區分)
    "所有權部_登記次序", "所有權部_登記日期", "所有權部_登記原因", "所有權部_原因發生日期",
    "所有權部_所有權人", "所有權部_統一編號", "所有權部_出生日期", "所有權部_住址",
    "所有權部_管理者", "所有權部_管理者統一編號", "所有權部_管理者住址",
    "所有權部_權利範圍", "所有權部_權狀字號", "所有權部_當期申報地價",
    "所有權部_前次移轉現值或原規定地價", "所有權部_歷次取得權利範圍", "所有權部_其他登記事項",
    # 頂層資訊 (結尾部分)
    "資料顯示完畢", "來源檔案"
]

def _extract_field(block_text: str, field_label: str, stop_keywords: List[str]) -> str:
    """
    一個通用的輔助函數，用於從文字區塊中提取指定欄位的內容。
    它會從欄位標籤開始，一直抓取到下一個停止關鍵字為止。
    """
    # 讓欄位標籤和停止關鍵字的匹配對空格不敏感
    # e.g., "地    目" -> "地\s*目"
    label_pattern = r'\s*'.join(field_label.split())
    stop_pattern = "|".join([r'\s*'.join(s.split()) for s in stop_keywords]) if stop_keywords else '(?!)' # (?!) 是一個永不匹配的模式
    
    pattern = re.compile(
        rf"{label_pattern}\s*[:：]?\s*(.*?)(?=\s*(?:{stop_pattern}|$))",
        re.DOTALL
    )
    match = pattern.search(block_text)
    if match:
        # 成功匹配，清理換行符、多餘的空格和所有的星號，並轉為半形
        cleaned_text = re.sub(r'\s+', ' ', match.group(1))
        raw_value = cleaned_text.replace('*', '').strip()
        value = raw_value.translate(FULL_TO_HALF)
        return value if value else "（空白）"
    
    # 如果找不到匹配項，回傳統一的失敗訊息
    return "無法擷取"

def parse_land_transcript(pdf_path: Path, debug_text: str = ""):
    """
    解析單一的地籍謄本 PDF 檔案，並回傳結構化資料。
    
    Args:
        pdf_path (Path): PDF 檔案的 Path 物件。        
    Returns:
        List[Dict[str, Any]]: 包含此 PDF 中所有解析資料的字典列表。
    """
    full_text = ""
    try:
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text(x_tolerance=2, y_tolerance=2, layout=True)
                if page_text:
                    # 如果檔名符合 debug_text，就印出該頁的原始文字
                    if debug_text and debug_text in pdf_path.name:
                        print(f"\n--- DEBUG: Raw text from page {page.page_number} of {pdf_path.name} ---\n{page_text}\n------------------------------------------------\n")
                    full_text += page_text + "\n"
    except Exception as e:
        print(f"  [錯誤] 讀取 PDF 檔案 '{pdf_path.name}' 時發生錯誤: {e}")
        return [] # 回傳空列表，確保型別一致性

    # --- 1. 將單一 PDF 依照謄本區間分割成多個獨立的謄本區塊 ---
    transcript_chunks = re.finditer(r"土\s*地\s*建\s*物\s*查\s*詢\s*資\s*料.*?〈\s*資料顯示完畢\s*〉", full_text, re.DOTALL)
    
    all_results_from_pdf = []

    # --- 2. 逐一處理每個謄本區塊 ---
    for match in transcript_chunks:
        chunk = match.group(0) # 取得完整的匹配文字

        # --- 2a. 在區塊內解析頂層資訊 ---
        header_match = re.search(r"(?P<district>\S+段)\s+(?P<land_number>[\d-]+)地號\s*資料查詢時間：(?P<query_time>.*?)\s*頁次：(?P<page_num>\d+)", chunk, re.DOTALL)
        if not header_match:
            continue # 如果這個區塊沒有地號，就跳過
        
        header_data = header_match.groupdict()
        land_number = header_data.get("land_number", "無法擷取")
        data_complete_marker = "是" if re.search(r'〈\s*資料顯示完畢\s*〉', chunk) else "否"

        # --- 2b. 在區塊內解析土地標示部 ---
        land_info = {}
        land_description_match = re.search(r"土地標示部(.*?(?=所有權部|他項權利部|$))", chunk, re.DOTALL)
        if land_description_match:
            land_description_text = land_description_match.group(1)
            
            for i, field in enumerate(ORDERED_LAND_FIELDS):
                # 停止關鍵字是所有後續的欄位
                stop_keywords = ORDERED_LAND_FIELDS[i+1:]
                # 將 '地    目' 這樣的鍵清理成 '地目'
                clean_field_key = field.replace(" ", "")
                value = _extract_field(land_description_text, field, stop_keywords)
                # 新增：清理 '其他登記事項' 中 '地號' 和 '土地' 之間的空格
                if clean_field_key == "其他登記事項":
                    value = re.sub(r'地號\s+土地', '地號土地', value)

                land_info[clean_field_key] = value
            
            # 後處理：修正公告土地現值的日期歸屬問題
            if "公告土地現值" in land_info and "使用地類別" in land_info:
                # 從 '使用地類別' 中尋找並提取日期
                date_match = re.search(r"(民國\s*\d+\s*年\s*\d+\s*月)", land_info["使用地類別"])
                if date_match:
                    date_str = date_match.group(1)
                    # 從 '使用地類別' 中移除日期
                    land_info["使用地類別"] = land_info["使用地類別"].replace(date_str, "").strip()
                    # 將日期加到 '公告土地現值' 的前面
                    land_info["公告土地現值"] = f"{date_str} {land_info['公告土地現值']}"

        # --- 2c. 在區塊內解析所有權部 ---
        ownership_parts = []
        ownership_sections = re.finditer(r"所有權部(.*?(?=(?:所有權部|他項權利部|$)))", chunk, re.DOTALL)
        for section_match in ownership_sections:
            section_text = section_match.group(1)
            # 使用最可靠的 '（登記次序）' 作為檢核點來分割區塊
            individual_owner_blocks = re.split(r'(?=\s*（\d+）登記次序)', section_text)

            # 從索引 1 開始迴圈，以捨棄由 split 產生的第一個空區塊
            for block in individual_owner_blocks[1:]:
                if not block.strip(): # 再次檢查，確保區塊不為空
                    continue

                owner_info = {}
                # 提取登記次序
                reg_order_match = re.search(r"（\d+）登記次序\s*[:：]\s*(\S+)", block)
                owner_info["登記次序"] = reg_order_match.group(1) if reg_order_match else "無法擷取"

                for i, field in enumerate(ORDERED_OWNER_FIELDS):
                    # 停止關鍵字是所有後續的欄位 + 最終標記
                    stop_keywords = ORDERED_OWNER_FIELDS[i+1:]
                    stop_keywords.append("〈 資料顯示完畢 〉")

                    clean_field_key = field.replace(" ","")
                    value = _extract_field(block, field, stop_keywords)

                    # 對 '權利範圍' 進行高精度目標提取，以清除跨頁雜訊
                    if clean_field_key == "權利範圍" and value != "無法擷取":
                        # 優先匹配 "分母 分之 分子" 格式
                        fraction_parts_match = re.search(r'(\d+)\s*分之\s*(\d+)', value)
                        if fraction_parts_match:
                            # 轉換為 "分子/分母" 格式
                            denominator = fraction_parts_match.group(1)
                            numerator = fraction_parts_match.group(2)
                            value = f"{numerator}/{denominator}"
                        elif "全部" in value:
                            value = "1/1"

                    # 清理 '其他登記事項' 的雜訊 (在半形化之後處理)
                    if clean_field_key == "其他登記事項" and value.startswith("(空白)"):
                        # 如果是 '(空白)' 後面跟著一小段無關字元，就還原成 '(空白)'
                        value = re.sub(r"^\(空白\)\s+[\w\d\s]+$", r"(空白)", value)
                    elif clean_field_key == "其他登記事項" and "地號 土地" in value:
                        value = re.sub(r'地號\s+土地', '地號土地', value)

                    # 清理特定欄位的雜訊
                    if clean_field_key == "原因發生日期" and "日" in value:
                        date_only_match = re.search(r'(民國\s*\d+\s*年\s*\d+\s*月\s*\d+\s*日)', value)
                        if date_only_match:
                            value = date_only_match.group(1)
                    
                    if clean_field_key == "前次移轉現值或原規定地價":
                        # 尋找完整的價格模式，忽略前面的雜訊
                        price_date_match = re.search(r'(\d+\s*年\s*\d+\s*月\s*[\d,.]+\s*元／平方公尺)', value)
                        if price_date_match:
                            value = price_date_match.group(1)

                    owner_info[clean_field_key] = value
                
                # 【強化穩定性】後處理，解析 '住址' 欄位中嵌套的 '管理者' 資訊
                try:
                    if '住址' in owner_info and isinstance(owner_info['住址'], str) and re.search(r'管\s*理\s*者', owner_info['住址']):
                        manager_text = owner_info['住址']
                        manager_match = re.search(r'管\s*理\s*者\s*[:：]?\s*(?P<name>.*?)\s*統一編號\s*[:：]?\s*(?P<id>\d+)\s*住\s*址\s*[:：]?\s*(?P<addr>.*)', manager_text)
                        if manager_match:
                            manager_data = manager_match.groupdict()
                            owner_info['管理者'] = manager_data.get('name', '無法擷取').strip()
                            owner_info['管理者統一編號'] = manager_data.get('id', '無法擷取').strip()
                            owner_info['管理者住址'] = manager_data.get('addr', '無法擷取').strip()
                            # 清理原始住址欄位，如果清理後為空，則給予提示
                            original_address = re.sub(r'管\s*理\s*者.*', '', manager_text).strip()
                            owner_info['住址'] = original_address if original_address else "（同管理者資訊）"
                        else:
                            # 雖然有 '管理者' 字樣，但格式不符
                            owner_info['管理者'] = "（格式不符無法解析）"
                except TypeError:
                    # 如果 '住址' 欄位不是字串 (例如，解析出錯變成其他類型)，則跳過管理者解析
                    print(f"  [警告] 在地號 {land_number} 的所有權部中，'住址' 欄位格式異常，跳過管理者解析。")

                ordered_owner_info = {key: owner_info.get(key, "無此資料項") for key in FINAL_OWNER_KEYS}
                
                ownership_parts.append(ordered_owner_info)

        # --- 2d. 將此區塊的解析結果加入列表 ---
        all_results_from_pdf.append({
            "地段": header_data.get("district", "無法擷取"),
            "地號": land_number,
            "資料查詢時間": " ".join(header_data.get("query_time", "無法擷取").split()),
            "土地標示部": land_info,
            "所有權部": ownership_parts,
            "資料顯示完畢": data_complete_marker,
            "來源檔案": pdf_path.name
        })

    return all_results_from_pdf

def create_final_table(data_list: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    將所有解析後的結構化資料列表扁平化為單一的 Pandas DataFrame。
    """
    records = []
    
    for data in data_list:
        if not data:
            continue

        # 準備帶有前綴的通用資訊
        land_info_prefixed = {f"標示部_{k}": v for k, v in data.get("土地標示部", {}).items()}
        
        common_info = {
            "地段": data.get("地段"),
            "地號": data.get("地號"),
            "資料查詢時間": data.get("資料查詢時間"),
            "資料顯示完畢": data.get("資料顯示完畢"),
            "來源檔案": data.get("來源檔案"),
        }
        common_info.update(land_info_prefixed)

        if not data.get("所有權部"):
            records.append(common_info)
        else:
            for owner_part in data["所有權部"]:
                owner_part_prefixed = {f"所有權部_{k}": v for k, v in owner_part.items()}
                flat_record = common_info.copy()
                flat_record.update(owner_part_prefixed)
                records.append(flat_record)
    
    if not records:
        return pd.DataFrame()

    df = pd.DataFrame(records)
    # 使用 reindex 確保所有檔案的欄位順序和完整性都一致
    return df.reindex(columns=CSV_COLUMN_ORDER)

def _split_district(full_district_str: str) -> (str, str):
    """
    輔助函數：將 '桃園市觀音區塔腳段' 拆分為 ('桃園市觀音區', '塔腳段')。
    """
    # 匹配到 '市', '區', '鎮', '鄉' 為止作為第一部分
    match = re.match(r'(.*?(?:市|區|鎮|鄉))(.*)', full_district_str)
    if match:
        return match.group(1).strip(), match.group(2).strip()
    # 如果沒有匹配到，提供一個備用方案
    return "未知行政區", full_district_str.strip()

def create_shareholder_appendix(data_list: List[Dict[str, Any]], output_dir: Path):
    """
    根據所有解析的資料，為每個地段建立一份 "共有土地共有人及對應持分附表" 的 Excel 檔案。
    (已更新，符合四點新需求)
    """
    if not data_list:
        return

    print("\n--- 正在產生 Excel 附表 (新版格式) ---")

    # 1. 將所有權人資料扁平化，方便排序和分組
    flat_owner_records = []
    for land_parcel in data_list:
        ownership_parts = land_parcel.get("所有權部", [])
        if not ownership_parts:
            continue # 跳過沒有所有權部的地號

        for owner_info in ownership_parts:
            manager = owner_info.get("管理者", "").strip()
            person_in_charge = manager if manager and "無法" not in manager and "格式不符" not in manager else owner_info.get("所有權人", "未知")
            
            flat_owner_records.append({
                "地段": land_parcel.get("地段", "未知地段"),
                "地號": land_parcel.get("地號", "未知地號"),
                "土地所有權人或管理人": person_in_charge,
                "持分": owner_info.get("權利範圍", "未知"),
            })

    if not flat_owner_records:
        print("  [注意] 沒有找到可用於產生附表的有效所有權人資料。")
        return

    # 2. 根據 "地段" 進行分組，為每個地段產生一個 Excel 檔案
    flat_owner_records.sort(key=itemgetter('地段'))
    for district, district_group in groupby(flat_owner_records, key=itemgetter('地段')):
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "共有土地共有人及對應持分附表"
        
        # 新版表頭
        headers = ["宗地流水號", "鄉鎮市區", "段小段名稱", "地號", "土地所有權人或管理人", "持分", "備註"]
        ws.append(headers)

        # 3. 在地段內，再根據 "地號" 進行分組，以彙總資料
        serial_counter = 1
        district_records = sorted(list(district_group), key=itemgetter('地號'))
        
        for land_number, land_group in groupby(district_records, key=itemgetter('地號')):
            land_records = list(land_group)
            
            # 彙總所有權人
            first_owner = land_records[0]['土地所有權人或管理人']
            total_owners = len(land_records)
            owner_display = f"{first_owner} 等{total_owners}人" if total_owners > 1 else first_owner

            # 彙總持分
            all_shares = "、".join([rec['持分'] for rec in land_records])

            # 拆分地段並格式化流水號
            township, section = _split_district(district)
            serial_number_str = f"{serial_counter:04d}"

            row_data = [serial_number_str, township, section, land_number, owner_display, all_shares, ""]
            ws.append(row_data)
            serial_counter += 1

        # 儲存 Excel 檔案
        output_excel_file = output_dir / f"土地清冊及持分附表--{district}.xlsx"
        wb.save(output_excel_file)
        print(f"📊 已產生 Excel 附表: {output_excel_file.name}")

def main():
    """主執行函數 - 處理多檔案並分別及合併產出"""
    project_dir = Path(__file__).parent
    input_dir = project_dir / "input_pdfs"
    output_dir = project_dir / "output"

    # 確保輸入/輸出目錄存在
    input_dir.mkdir(exist_ok=True)
    output_dir.mkdir(exist_ok=True)

    pdf_files = list(input_dir.glob("*.pdf"))
    if not pdf_files:
        print(f"錯誤：在資料夾 '{input_dir}' 中找不到任何 PDF 檔案。")
        print("請將您的 PDF 謄本檔案複製到該資料夾中。")
        return

    print(f"找到 {len(pdf_files)} 個 PDF 檔案，開始逐一處理...")

    all_parsed_data = []
    successful_files = 0

    for pdf_path in pdf_files:
        print(f"\n--- 正在處理: {pdf_path.name} ---")
        # parse_land_transcript 回傳一個包含該 PDF 中所有地號資料的列表
        parsed_data_from_this_pdf = parse_land_transcript(pdf_path)
        
        if not parsed_data_from_this_pdf:
            print(f"  [注意] 未能從 '{pdf_path.name}' 中解析出任何資料。")
            continue

        # --- 為單一檔案產生獨立報告 ---
        # 檔名以第一個解析到的地段為基礎，若無則用原檔名
        base_name = parsed_data_from_this_pdf[0].get("地段", pdf_path.stem).replace(" ", "")
        output_csv_file = output_dir / f"{base_name}.csv"
        output_json_file = output_dir / f"{base_name}.json"

        # 建立此單一檔案的 DataFrame
        df_single = create_final_table(parsed_data_from_this_pdf)
        
        # 儲存獨立的 JSON 檔案
        with open(output_json_file, 'w', encoding='utf-8') as f:
            json.dump(parsed_data_from_this_pdf, f, ensure_ascii=False, indent=4)
        print(f"  -> 已產生獨立 JSON 報告: {output_json_file.name}")

        # 儲存獨立的 CSV 檔案
        df_single.to_csv(output_csv_file, index=False, encoding='utf-8-sig')
        print(f"  -> 已產生獨立 CSV 報告: {output_csv_file.name}")

        # 將此檔案的解析結果加入總列表，以備合併使用
        all_parsed_data.extend(parsed_data_from_this_pdf)
        successful_files += 1

    # --- 處理流程結束後，根據檔案數量決定是否產生合併報告 ---
    if not all_parsed_data:
        print("\n處理完成，但未能從任何檔案中成功解析資料。")
        return

    # 如果處理了超過一個檔案，就額外產生合併報告
    if len(pdf_files) > 1:
        print(f"\n--- 正在產生合併報告 ---")
        
        # 決定合併檔名
        consolidated_csv_file = output_dir / "consolidated_land_data.csv"
        consolidated_json_file = output_dir / "diagnostic_data.json"

        # 建立最終的合併 DataFrame
        final_df = create_final_table(all_parsed_data)

        # 儲存合併後的 JSON 診斷檔案
        with open(consolidated_json_file, 'w', encoding='utf-8') as f:
            json.dump(all_parsed_data, f, ensure_ascii=False, indent=4)
        print(f"📝 合併的 JSON 檔案已儲存至:\n{consolidated_json_file.resolve()}")

        # 儲存合併後的 CSV 檔案
        final_df.to_csv(consolidated_csv_file, index=False, encoding='utf-8-sig')
        print(f"✅ 合併的 CSV 檔案已儲存至:\n{consolidated_csv_file.resolve()}")

    # --- 新增：在所有處理結束後，產生 Excel 附表 ---
    create_shareholder_appendix(all_parsed_data, output_dir)

    # --- 最終總結 ---
    print("\n" + "="*50)
    print("✅ 處理完成！")
    print(f"總共掃描 {len(pdf_files)} 個 PDF 檔案，成功處理 {successful_files} 個。")
    if len(pdf_files) > 1 and successful_files > 1:
        print("已為每個成功處理的檔案產生獨立報告，並額外產生一份合併報告。")
    elif successful_files > 0:
        print("已為成功處理的檔案產生獨立報告。")
    
    total_records = len(create_final_table(all_parsed_data)) if all_parsed_data else 0
    print(f"總共成功解析 {len(all_parsed_data)} 個地號區塊，產生 {total_records} 筆記錄。")
    print("="*50)

if __name__ == "__main__":
    main()
