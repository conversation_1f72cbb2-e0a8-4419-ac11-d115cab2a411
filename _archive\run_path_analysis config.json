{"general_settings": {"api_key_env_variable": "ORS_API_KEY", "output_base_folder": "C:/#ck project/CK_Land_expropriation/runpath_results/", "facility_points_file": "C:/#ck project/CK_Land_expropriation/runpath_input/start_points.csv"}, "analysis_tasks": [{"task_name": "徵收土地地籍謄本", "enabled": true, "input_shapefile": "C:/#ck project/CK_Land_expropriation/gis_input/徵收土地地籍謄本.shp", "fields": {"land_id": "Parcel_ID", "centroid_x": "Centroid_X", "centroid_y": "Centroid_Y"}, "output_filename_prefix": "徵收土地地籍謄本"}, {"task_name": "土地買賣實例", "enabled": true, "input_shapefile": "C:/#ck project/CK_Land_expropriation/gis_input/土地買賣實例.shp", "fields": {"land_id": "Case_ID", "centroid_x": "Centroid_X", "centroid_y": "Centroid_Y"}, "output_filename_prefix": "土地買賣實例"}, {"task_name": "地價區段範圍", "enabled": false, "input_shapefile": "C:/#ck project/CK_Land_expropriation/gis_input/地價區段範圍.shp", "fields": {"land_id": "Section_ID", "centroid_x": "Centroid_X", "centroid_y": "Centroid_Y"}, "output_filename_prefix": "地價區段範圍"}]}