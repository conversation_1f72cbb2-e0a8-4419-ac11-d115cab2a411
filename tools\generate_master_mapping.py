# ===================================================================
#  檔案名稱: generate_master_mapping.py
#  版    本: v3.0 - Master Plan Centric
#  說    明: 終極版腳本。以「基準表」為核心，整合所有業務指標與
#            GIS 欄位，建立一個由業務驅動的、完整的對照表草稿。
# ===================================================================

import pandas as pd
from pathlib import Path
from collections import defaultdict

def generate_master_mapping():
    """
    主函式，以基準表為核心，執行掃描、彙總並生成主欄位對照表草稿。
    """
    try:
        BASE_DIR = Path(__file__).resolve().parent
    except NameError:
        BASE_DIR = Path.cwd()

    TEMPLATES_DIR = BASE_DIR / "form_input" / "templates"
    MASTER_PLAN_FILE = TEMPLATES_DIR / "表3_地價區段勘查表_基準表.xlsx"
    OUTPUT_FILE = BASE_DIR / "master_field_mapping_draft.xlsx"

    print("=== 開始掃描 (v3.0 - 以基準表為核心) ===")

    # --- 步驟一: 讀取基準表作為權威來源 ---
    if not MASTER_PLAN_FILE.is_file():
        print(f"錯誤: 找不到核心的基準表檔案 '{MASTER_PLAN_FILE}'。")
        return

    print(f"1. 正在讀取權威來源: {MASTER_PLAN_FILE.name}")
    try:
        master_plan_df = pd.read_excel(MASTER_PLAN_FILE)
        # 預處理：按 ItemName 聚合，將 UseType 合併
        authoritative_indicators = master_plan_df.groupby('ItemName').agg({
            'ItemClass': 'first',
            'UseType': lambda x: ', '.join(x.unique())
        }).reset_index()
    except Exception as e:
        print(f"  - 讀取基準表失敗: {e}"); return
    
    # --- 步驟二: 掃描所有 mapping 檔案以收集關聯資訊 ---
    print("2. 正在掃描所有 `*_mapping.xlsx` 檔案以收集關聯資訊...")
    
    # 結構: {'指標/欄位名': {'type': ..., 'forms': set(), 'related_gis_field': str}}
    collected_data = defaultdict(lambda: {'forms': set()})
    
    # 預先填入基準表中的所有指標
    for _, row in authoritative_indicators.iterrows():
        item_name = row['ItemName']
        collected_data[item_name]['type'] = 'indicator'
        collected_data[item_name]['item_class'] = row['ItemClass']
        collected_data[item_name]['use_types'] = row['UseType']

    mapping_files = list(TEMPLATES_DIR.glob("*_mapping.xlsx"))
    if not mapping_files:
        print("  - 警告: 未找到任何 mapping 檔案。"); return

    for file_path in mapping_files:
        form_name = file_path.stem.replace('_mapping', '')
        try:
            xls = pd.ExcelFile(file_path)
            # 遍歷所有工作表和欄位，寻找關聯
            for sheet_name in xls.sheet_names:
                df = pd.read_excel(xls, sheet_name=sheet_name)
                # 尋找 ItemName 和 Source_GIS_Field 的配對
                if 'ItemName' in df.columns and 'Source_GIS_Field' in df.columns:
                    for _, row in df.iterrows():
                        item_name = str(row['ItemName']).strip()
                        gis_field = str(row['Source_GIS_Field']).strip()
                        if item_name and gis_field and gis_field != 'nan':
                            if item_name in collected_data:
                                collected_data[item_name]['related_gis_field'] = gis_field
                                collected_data[gis_field]['forms'].add(form_name) # GIS欄位被此表單使用
                                collected_data[item_name]['forms'].add(form_name) # 指標也被此表單使用
                            else:
                                print(f"  - 警告: Mapping 檔案 '{file_path.name}' 中的 ItemName '{item_name}' 不存在於基準表中，已忽略。")
                
                # 尋找其他 Source Field
                for col_name in ['Source Field', 'Source_GIS_Field']:
                    if col_name in df.columns:
                        for field in df[col_name].dropna().unique():
                            field_str = str(field).strip()
                            if field_str:
                                collected_data[field_str]['type'] = 'field'
                                collected_data[field_str]['forms'].add(form_name)

        except Exception as e:
            print(f"    - 處理檔案 {file_path.name} 時發生錯誤: {e}")

    # --- 步驟三: 整理並生成 Excel 草稿 ---
    print("3. 正在整理數據並生成 Excel 草稿檔案...")
    
    output_data = []
    # 以基準表中的指標順序為主
    for item_name in authoritative_indicators['ItemName']:
        data = collected_data[item_name]
        gis_field = data.get('related_gis_field', '')
        
        # 如果一個 GIS 欄位被多個指標關聯 (罕見但可能)，需要警告
        if not gis_field: # 如果沒有關聯的 GIS 欄位，可能是計算項
            gis_field = 'N/A'
            source_layer = 'N/A (Calculated)'
        else:
            source_layer = '' # 留空給使用者填寫

        output_data.append({
            'Canonical_Name': '',
            'DB_Field_Name': '',
            'Source_Layer_Key': source_layer,
            'Source_Field_Name': gis_field,
            'ItemClass': data.get('item_class', ''),
            'ItemName': item_name,
            'UseTypes': data.get('use_types', ''),
            'Description': '',
            'Used_In_Forms': ', '.join(sorted(list(data['forms'])))
        })

    # 處理那些在基準表中沒有，但 mapping 裡有的獨立 GIS 欄位
    for key, data in sorted(collected_data.items()):
        is_indicator = data.get('type') == 'indicator'
        is_processed = any(d['ItemName'] == key or d['Source_Field_Name'] == key for d in output_data)
        if not is_indicator and not is_processed:
             output_data.append({
                'Canonical_Name': '', 'DB_Field_Name': '', 'Source_Layer_Key': '',
                'Source_Field_Name': key,
                'ItemClass': 'N/A', 'ItemName': 'N/A', 'UseTypes': 'N/A',
                'Description': '',
                'Used_In_Forms': ', '.join(sorted(list(data['forms'])))
            })

    output_df = pd.DataFrame(output_data)

    try:
        with pd.ExcelWriter(OUTPUT_FILE, engine='openpyxl') as writer:
            output_df.to_excel(writer, sheet_name='Field_Info', index=False)
            
            layer_info_template = pd.DataFrame({
                'Source_Layer_Key': ['main_parcels', 'sales_cases', 'project_info'],
                'Layer_Name_ZH': ['徵收土地地籍', '買賣案例', '專案全域資訊'],
                'Default_Path': ['gis_data/your_parcels.shp', 'gis_data/your_sales.shp', 'project_config.json'],
                'ID_Field_Canonical': ['parcel_id', 'case_id', 'project_id']
            })
            layer_info_template.to_excel(writer, sheet_name='Layer_Info', index=False)

        print(f"\n成功！主欄位對照表草稿已儲存至:\n{OUTPUT_FILE}")
        print("\n下一步驟:")
        print("1. 打開該檔案，檢視 'Field_Info' 工作表。")
        print("2. 數據已按「基準表」中的業務指標進行組織。")
        print("3. 為每一行填寫 'Canonical_Name', 'DB_Field_Name', 'Source_Layer_Key' 等空白欄位。")
        print("4. 完成後，將檔案重命名為 'master_field_mapping.xlsx'。")
    except Exception as e:
        print(f"\n錯誤: 無法寫入 Excel 檔案: {e}")


if __name__ == "__main__":
    generate_master_mapping()