# ==============================================================================
# 程式名稱: regenerate_reports.py
# 版本: 2.4.0 (Final Feature Sync)
# 最後修訂日期: 2025-07-29
#
# 用途:
#   - [功能同步] 新增生成 "各地段對應持分附表(私有地)" 的功能，與主腳本完全一致。
#   - [修正] 徹底修復資料遺失 Bug，直接使用 DataFrame 進行所有操作。
#   - [調整] 將新增欄位移至表格末尾，並更名為 '宗地流水號_私'。
#   - [升級] 全面同步更新所有檔案格式 (xlsx, csv, json)。
# ==============================================================================

import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment
from openpyxl.utils import get_column_letter
from pathlib import Path
from itertools import groupby
from operator import itemgetter
import math
import re
import os
import json

def _split_district(full_district_str: str) -> tuple[str, str]:
    if not isinstance(full_district_str, str): return "未知行政區", "未知地段"
    match = re.match(r'(.*?(?:市|區|鎮|鄉))(.*)', full_district_str)
    if match: return match.group(1).strip(), match.group(2).strip()
    return "未知行政區", full_district_str.strip()

def _format_share(share_str: str) -> str:
    if not isinstance(share_str, str): return "格式錯誤"
    if "全部" in str(share_str): return "1/1"
    fraction_match = re.search(r'(\d+)\s*分之\s*(\d+)', str(share_str))
    if fraction_match: return f"{fraction_match.group(2)}/{fraction_match.group(1)}"
    return str(share_str).strip()

def _write_printable_sheet(ws, district_records, land_summary_map, project_name):
    ws.delete_rows(1, ws.max_row + 1)
    ws.delete_cols(1, ws.max_column + 1)
    ROWS_PER_CHUNK, COLS_PER_DATA_BLOCK, BLOCKS_PER_ROW = 25, 3, 4
    PAGE_ROW_GAP, HEADER_ROWS, MAX_ROWS_PER_PAGE = 2, 5, 34
    font_bold_large = Font(name='標楷體', bold=True, size=16)
    font_bold_normal = Font(name='標楷體', bold=True, size=12)
    font_normal = Font(name='標楷體', size=11)
    align_center = Alignment(horizontal='center', vertical='center')
    align_left = Alignment(horizontal='left', vertical='center')
    for i in range(BLOCKS_PER_ROW):
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 1)].width = 5
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 2)].width = 16
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 3)].width = 12
    plots_data = {k: list(v) for k, v in groupby(district_records, key=itemgetter('宗地流水號'))}
    plot_keys = sorted(plots_data.keys(), key=lambda x: str(x))
    current_page_start_row, current_col_offset = 1, 0
    row_heights_in_current_page_row = [0]
    def write_page_headers(start_row):
        ws.merge_cells(start_row=start_row, start_column=1, end_row=start_row, end_column=12)
        cell = ws.cell(start_row, 1, "共有土地共有人及對應持分附表"); cell.font = font_bold_large; cell.alignment = align_center
        ws.merge_cells(start_row=start_row+1, start_column=1, end_row=start_row+1, end_column=12)
        cell = ws.cell(start_row+1, 1, project_name); cell.font = font_normal; cell.alignment = align_left
        return start_row + 3
    current_row = write_page_headers(current_page_start_row)
    for serial_no in plot_keys:
        plot_records = plots_data[serial_no]
        if not plot_records: continue
        num_chunks = math.ceil(len(plot_records) / ROWS_PER_CHUNK)
        rows_needed_for_block = HEADER_ROWS + ROWS_PER_CHUNK
        if current_col_offset > 0 and (current_col_offset + COLS_PER_DATA_BLOCK * num_chunks > COLS_PER_DATA_BLOCK * BLOCKS_PER_ROW):
             current_row += max(row_heights_in_current_page_row) + PAGE_ROW_GAP
             current_col_offset, row_heights_in_current_page_row = 0, [0]
        if current_row - current_page_start_row + rows_needed_for_block > MAX_ROWS_PER_PAGE:
            current_page_start_row = ws.max_row + PAGE_ROW_GAP + 1 if ws.max_row > 1 else MAX_ROWS_PER_PAGE
            current_row = write_page_headers(current_page_start_row)
            current_col_offset, row_heights_in_current_page_row = 0, [0]
        start_col = 1 + current_col_offset
        record_sample = plot_records[0]
        for i, label in enumerate(["宗地流水號", "鄉鎮市區", "段小段名稱", "地號"]):
            ws.cell(current_row + i, start_col, label).font = font_bold_normal
            ws.merge_cells(start_row=current_row + i, start_column=start_col + 1, end_row=current_row + i, end_column=start_col + COLS_PER_DATA_BLOCK * num_chunks - 1)
            ws.cell(current_row + i, start_col + 1, record_sample.get(label, "")).font = font_normal if i > 0 else font_bold_normal
        data_start_row = current_row + HEADER_ROWS
        for i, record in enumerate(plot_records):
            chunk_idx, row_in_chunk = divmod(i, ROWS_PER_CHUNK)
            col_offset_in_block = chunk_idx * COLS_PER_DATA_BLOCK
            if row_in_chunk == 0:
                summary_text = land_summary_map.get((record['地段'], record['地號']), '')
                for j, sub_header in enumerate(["土地所有權人或管理人", summary_text, "持分"]):
                    ws.cell(data_start_row - 1, start_col + col_offset_in_block + j, sub_header).font = font_bold_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block, i + 1).font = font_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block + 1, record['土地所有權人或管理人']).font = font_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block + 2, record['持分']).font = font_normal
        current_col_offset += COLS_PER_DATA_BLOCK * num_chunks
        row_heights_in_current_page_row.append(rows_needed_for_block)

def generate_excel_report(df_source: pd.DataFrame, output_path: Path, project_name: str):
    if df_source.empty:
        pd.DataFrame(columns=df_source.columns).to_excel(output_path, sheet_name='各地段土地謄本', index=False)
        return

    def prepare_printable_sheet_data(df: pd.DataFrame):
        all_records = []
        for _, row in df.iterrows():
            manager = row.get('所有權部_管理者')
            owner = row.get('所有權部_所有權人')
            person_in_charge = manager if pd.notna(manager) and manager not in ["", "此項無資料", "(空白)"] else owner
            all_records.append({'地段': row.get('地段'), '地號': row.get('地號'), '登記次序': row.get('所有權部_登記次序'), '宗地流水號': row.get('宗地流水號'), '土地所有權人或管理人': person_in_charge, '持分_原始': row.get('所有權部_權利範圍')})
        
        district_records_list = sorted(all_records, key=lambda r: (str(r.get('地段','')), str(r.get('地號','')), int(str(r.get('登記次序', '0')).split('(')[0]) if str(r.get('登記次序', '0')).split('(')[0].isdigit() else 9999))
        land_summary_map = {}
        for land_id, group in groupby(district_records_list, key=lambda r: (r['地段'], r['地號'])):
            group_list = list(group)
            if not group_list: continue 
            count = len(group_list)
            first_owner_name = group_list[0].get('土地所有權人或管理人', "無法擷取") 
            land_summary_map[land_id] = f"{first_owner_name} 等{count}人" if count > 1 else first_owner_name
        
        for record in district_records_list:
            record['鄉鎮市區'], record['段小段名稱'] = _split_district(record.get('地段', ''))
            record['持分'] = _format_share(record.get('持分_原始', ''))
            
        return district_records_list, land_summary_map

    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        df_source_with_summary = df_source.copy()
        _, land_summary_map_all = prepare_printable_sheet_data(df_source_with_summary)
        df_source_with_summary['總共有人數'] = df_source_with_summary.apply(lambda row: land_summary_map_all.get((row['地段'], row['地號']), "此項無資料"), axis=1)
        df_source_with_summary.to_excel(writer, sheet_name='各地段土地謄本', index=False)
        
        district_records_list_all, _ = prepare_printable_sheet_data(df_source)
        appendix_rows, headers = [], ["宗地流水號", "鄉鎮市區", "段小段名稱", "地號", "登記次序", "土地所有權人或管理人", "持分", "有權人與持分彙總"]
        processed_summaries = set()
        for record in district_records_list_all:
            land_id = (record['地段'], record['地號'])
            summary = land_summary_map_all.get(land_id, "") if land_id not in processed_summaries else ""
            if summary: processed_summaries.add(land_id)
            appendix_rows.append([record.get('宗地流水號'), record.get('鄉鎮市區'), record.get('段小段名稱'), record.get('地號'), record.get('登記次序'), record.get('土地所有權人或管理人'), record.get('持分'), summary])
        df_appendix = pd.DataFrame(appendix_rows, columns=headers)
        df_appendix.to_excel(writer, sheet_name='各地段對應持分附表', index=False)
        
        wb = writer.book
        ws_printable_all = wb.create_sheet("各地段對應持分附表(可列印)")
        _write_printable_sheet(ws_printable_all, district_records_list_all, land_summary_map_all, project_name)

        df_private = df_source[df_source['公私有別'] == '私有地'].copy()
        if not df_private.empty:
            district_records_list_private, land_summary_map_private = prepare_printable_sheet_data(df_private)
            ws_printable_private = wb.create_sheet("各地段對應持分附表(私有地)")
            _write_printable_sheet(ws_printable_private, district_records_list_private, land_summary_map_private, project_name)

def add_custom_columns(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty: return df
    def classify_ownership(uni_id: any) -> str:
        if not isinstance(uni_id, str) or pd.isna(uni_id): return "私有地"
        uni_id = str(uni_id).strip()
        if uni_id == "0000000158": return "公有地"
        else: return "私有地"
    df['公私有別'] = df['所有權部_統一編號'].apply(classify_ownership)
    df['宗地流水號_私'] = ''
    private_land_df = df[df['公私有別'] == '私有地'].copy()
    if not private_land_df.empty:
        private_unique_plots = private_land_df[['地段', '地號']].drop_duplicates().sort_values(by=['地段', '地號'])
        private_serial_map = {tuple(row): f"{i+1:04d}" for i, row in enumerate(private_unique_plots.itertuples(index=False))}
        private_indices = df['公私有別'] == '私有地'
        private_plot_tuples = pd.Series(list(zip(df.loc[private_indices, '地段'], df.loc[private_indices, '地號'])))
        df.loc[private_indices, '宗地流水號_私'] = private_plot_tuples.map(private_serial_map)
    return df

def df_to_list_of_dicts(df: pd.DataFrame) -> list:
    all_data_list = []
    records = df.to_dict('records')
    for key, group in groupby(records, key=lambda x: (x.get('地段'), x.get('地號'))):
        group_list = list(group)
        if not group_list: continue
        first_row = group_list[0]
        land_dict = {
            '地段': first_row.get('地段'), '地號': first_row.get('地號'),
            '宗地流水號': first_row.get('宗地流水號'), '資料查詢時間': first_row.get('資料查詢時間'),
            '資料顯示完畢': first_row.get('資料顯示完畢'), '來源檔案': first_row.get('來源檔案'),
            '土地標示部': { k.replace('標示部_', ''): v for k, v in first_row.items() if str(k).startswith('標示部_') },
            '所有權部': [ {k.replace('所有權部_', ''): v for k, v in owner_row.items() if str(k).startswith('所有權部_')} for owner_row in group_list ]
        }
        all_data_list.append(land_dict)
    return all_data_list

def main():
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("錯誤：找不到 config.json 檔案。")
        return

    output_dir = Path(config.get('output_dir', 'output'))
    consolidated_file = output_dir / "consolidated_land_data.xlsx"

    if not consolidated_file.exists():
        print(f"錯誤：找不到合併報告 '{consolidated_file}'。")
        return

    print(f"--- 正在讀取主檔案: {consolidated_file.name} ---")
    try:
        df_master = pd.read_excel(consolidated_file, sheet_name='各地段土地謄本', engine='openpyxl', dtype=str)
        df_master = df_master.fillna('')
    except Exception as e:
        print(f"  [錯誤] 讀取主檔案失敗: {e}")
        return

    project_name = config.get("project_name", "預設專案名稱")

    print("\n--- 步驟 1: 重新計算欄位並格式化 ---")
    cols_to_drop = ['公私有別', '宗地流水號_私', '宗地流水號公私有別', '總共有人數']
    df_master = df_master.drop(columns=[col for col in cols_to_drop if col in df_master.columns], errors='ignore')
    df_master_updated = add_custom_columns(df_master)
    
    if '宗地流水號' in df_master_updated.columns:
        df_master_updated['宗地流水號'] = df_master_updated['宗地流水號'].astype(str).str.zfill(4)
    if '所有權部_登記次序' in df_master_updated.columns:
        df_master_updated['所有權部_登記次序'] = df_master_updated['所有權部_登記次序'].astype(str)
    
    print("  [成功] 欄位已重新計算並格式化。")
    
    all_data_list = df_to_list_of_dicts(df_master_updated)

    print(f"\n--- 步驟 2: 更新合併報告所有格式檔案 ---")
    df_master_updated.to_csv(output_dir / "consolidated_land_data.csv", index=False, encoding='utf-8-sig')
    with open(output_dir / "consolidated_land_data.json", 'w', encoding='utf-8') as f:
        json.dump(all_data_list, f, ensure_ascii=False, indent=4)
    generate_excel_report(df_master_updated, consolidated_file, project_name)
    print("  [成功] 合併報告 .xlsx, .csv, .json 已完全同步。")

    grouped = df_master_updated.groupby('地段')
    print(f"\n--- 步驟 3: 根據主檔案重新生成 {len(grouped)} 個獨立報告 ---")

    for district_name, df_group in grouped:
        if pd.isna(district_name) or district_name == '': continue
        base_name = str(district_name).replace(" ", "")
        output_path_xlsx = output_dir / f"土地清冊及持分附表--{base_name}.xlsx"
        output_path_csv = output_dir / f"{base_name}.csv"
        output_path_json = output_dir / f"{base_name}.json"
        
        print(f"  -> 正在生成: {base_name} 的所有格式報告")
        
        district_data_list = [item for item in all_data_list if item.get('地段') == district_name]
        
        df_group.to_csv(output_path_csv, index=False, encoding='utf-8-sig')
        with open(output_path_json, 'w', encoding='utf-8') as f:
            json.dump(district_data_list, f, ensure_ascii=False, indent=4)
        generate_excel_report(df_group, output_path_xlsx, project_name)

    print("\n✅ 所有報告已根據合併主檔案更新完成！")

if __name__ == "__main__":
    main()