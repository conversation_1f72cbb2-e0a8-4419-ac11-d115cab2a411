import os
import math
import shutil
from osgeo import ogr, gdal

# --------------------------------------------------------------------------
# ==                   核心分析函数 (处理单个文件)                      ==
# --------------------------------------------------------------------------
def rotate_point(p, angle_rad, center_p=(0, 0)):
    """辅助函数：以一个中心点旋转指定的点"""
    x, y = p[0] - center_p[0], p[1] - center_p[1]
    cos_a, sin_a = math.cos(angle_rad), math.sin(angle_rad)
    new_x = x * cos_a - y * sin_a + center_p[0]
    new_y = x * sin_a + y * cos_a + center_p[1]
    return (new_x, new_y)

def process_single_shapefile(shapefile_path: str, fill_ratio_threshold: float, convexity_threshold: float):
    """
    对单一 Shapefile 进行所有指标计算和分类的核心函数。
    """
    gdal.UseExceptions()
    
    try:
        dataSource = ogr.Open(shapefile_path, 1) # 以更新模式打开
        if dataSource is None:
            print(f"  > 错误：无法打开文件 {os.path.basename(shapefile_path)}")
            return
        layer = dataSource.GetLayer()
    except Exception as e:
        print(f"  > 打开文件时发生错误: {e}"); return

    layer_defn = layer.GetLayerDefn()
    
    fields_to_create_ordered = [
        ("Centroid_X", {"type": ogr.OFTReal, "width": 12, "precision": 2}),
        ("Centroid_Y", {"type": ogr.OFTReal, "width": 12, "precision": 2}),
        ("OMBB_Long",  {"type": ogr.OFTReal, "width": 10, "precision": 1}),
        ("OMBB_Short", {"type": ogr.OFTReal, "width": 10, "precision": 1}),
        ("Width",      {"type": ogr.OFTReal, "width": 10, "precision": 1}),
        ("Depth",      {"type": ogr.OFTReal, "width": 10, "precision": 1}),
        ("FillRatio",  {"type": ogr.OFTReal, "width": 8, "precision": 1}),
        ("ShapeRatio", {"type": ogr.OFTReal, "width": 8, "precision": 1}),
        ("Convexity",  {"type": ogr.OFTReal, "width": 8, "precision": 1}),
        ("AssessType", {"type": ogr.OFTString, "width": 20}),
        ("Shape_Type", {"type": ogr.OFTString, "width": 20}),
    ]

    existing_fields = {layer_defn.GetFieldDefn(i).GetName() for i in range(layer_defn.GetFieldCount())}
    
    for name, props in fields_to_create_ordered:
        if name not in existing_fields:
            field_defn = ogr.FieldDefn(name, props['type'])
            if props['type'] == ogr.OFTReal:
                field_defn.SetWidth(props['width']); field_defn.SetPrecision(props['precision'])
            else:
                field_defn.SetWidth(props['width'])
            layer.CreateField(field_defn)

    feature_count = layer.GetFeatureCount()
    layer.ResetReading()

    processed_count = 0
    for i, feature in enumerate(layer):
        geom = feature.GetGeometryRef()
        if geom is None or geom.IsEmpty() or geom.GetArea() < 1e-6: continue

        try:
            # --- 步骤 1: 计算所有基础几何指标 ---
            area_real = geom.GetArea()
            convex_hull = geom.ConvexHull()
            if convex_hull is None or convex_hull.IsEmpty(): continue
            
            area_convex_hull = convex_hull.GetArea()
            convexity_ratio = area_real / area_convex_hull if area_convex_hull > 1e-9 else 0.0
            
            ch_ring = convex_hull.GetGeometryRef(0)
            if ch_ring is None or ch_ring.GetPointCount() <= 2: continue
            ch_points = ch_ring.GetPoints()
            
            min_area, omBB_long, omBB_short, best_angle_rad = float('inf'), 0.0, 0.0, 0.0

            for j in range(len(ch_points) - 1):
                p1, p2 = ch_points[j], ch_points[j+1]
                angle = math.atan2(p2[1] - p1[1], p2[0] - p1[0])
                rotated_ch_points = [rotate_point(p, -angle) for p in ch_points]
                
                min_x, max_x = min(p[0] for p in rotated_ch_points), max(p[0] for p in rotated_ch_points)
                min_y, max_y = min(p[1] for p in rotated_ch_points), max(p[1] for p in rotated_ch_points)
                
                width_tmp, height_tmp = max_x - min_x, max_y - min_y
                area = width_tmp * height_tmp
                
                if area < min_area:
                    min_area, omBB_long, omBB_short = area, max(width_tmp, height_tmp), min(width_tmp, height_tmp)
                    best_angle_rad = angle if width_tmp > height_tmp else angle + math.pi / 2

            area_omBB = omBB_long * omBB_short
            fill_ratio = area_real / area_omBB if area_omBB > 1e-9 else 0.0
            
            rotation_rad = -best_angle_rad
            original_boundary = geom.GetBoundary()
            if original_boundary is None: continue
            original_points = original_boundary.GetPoints()
            if not original_points: continue
            
            rotated_points = [rotate_point(p, rotation_rad) for p in original_points]
            
            r_min_x, r_max_x = min(p[0] for p in rotated_points), max(p[0] for p in rotated_points)
            r_min_y, r_max_y = min(p[1] for p in rotated_points), max(p[1] for p in rotated_points)
            
            depth = r_max_y - r_min_y
            width = r_max_x - r_min_x
            
            longer_dim, shorter_dim = max(depth, width), min(depth, width)
            aspect_ratio = longer_dim / shorter_dim if shorter_dim > 1e-9 else float('inf')
            
            centroid = geom.Centroid()
            cx, cy = centroid.GetX(), centroid.GetY()

            # --- 步骤 2: 進行雙重分類 ---
            if convexity_ratio < 0.95:
                assessed_shape = "凹陷圖形"
            elif fill_ratio >= 0.85:
                assessed_shape = "矩形"
            else:
                assessed_shape = "凸邊非矩形"

            shape_type = "不規則形"
            if convexity_ratio >= convexity_threshold:
                if fill_ratio >= fill_ratio_threshold:
                    shape_type = "方形"
            
            # --- 步骤 3: 回填所有数据 ---
            feature.SetField("Centroid_X", cx); feature.SetField("Centroid_Y", cy)
            feature.SetField("OMBB_Long", omBB_long); feature.SetField("OMBB_Short", omBB_short)
            feature.SetField("Width", width); feature.SetField("Depth", depth)
            feature.SetField("FillRatio", fill_ratio)
            feature.SetField("ShapeRatio", aspect_ratio)
            feature.SetField("Convexity", convexity_ratio)
            feature.SetField("AssessType", assessed_shape)
            feature.SetField("Shape_Type", shape_type)
            
            layer.SetFeature(feature)
            processed_count += 1
        except Exception:
            continue
    
    print(f"  > 共 {feature_count} 个图征，成功处理 {processed_count} 个。")
    dataSource = None

# --------------------------------------------------------------------------
# ==                   主程序执行区块 (批次处理流程)                       ==
# --------------------------------------------------------------------------
if __name__ == "__main__":
    
    # =========================================================================
    # ==                      【步骤 1】请设定路径和阈值                     ==
    # =========================================================================
    
    # 设定您的项目根目录
    project_root = r"C:\#ck project\CK_Land_expropriation"

    # 自动定义输入和输出文件夹路径
    input_dir = os.path.join(project_root, "gis_input")
    output_dir = os.path.join(project_root, "gis_output")

    # 最终分类 (Shape_Type) 的规则阈值
    CONVEXITY_THRESHOLD = 0.95
    FILL_RATIO_THRESHOLD = 0.75
    
    # =========================================================================
    
    # --- 批次处理主流程 ---
    print("--- 开始批次处理 Shapefile ---")
    
    os.makedirs(input_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)

    shapefiles_to_process = [f for f in os.listdir(input_dir) if f.lower().endswith(".shp")]

    if not shapefiles_to_process:
        print(f"\n错误：在 '{input_dir}' 中找不到任何 .shp 文件。")
        print("请将您要处理的 Shapefile (包含 .shx, .dbf 等) 放入该文件夹。")
    else:
        print(f"找到 {len(shapefiles_to_process)} 个待处理的 Shapefile...")

        for shp_name in shapefiles_to_process:
            print(f"\n[ 正在处理: {shp_name} ]")
            
            base_name = os.path.splitext(shp_name)[0]
            input_base_path = os.path.join(input_dir, base_name)
            output_base_path = os.path.join(output_dir, base_name)

            try:
                # 寻找所有相关的sidecar文件并复制
                related_files = [f for f in os.listdir(input_dir) if f.startswith(base_name + '.')]
                for file_name in related_files:
                    shutil.copy2(os.path.join(input_dir, file_name), output_dir)
                
                print(f"  > 已将原始文件 '{shp_name}' 及其相关文件复制到输出目录...")
                
                # 在复制后的文件上进行处理
                output_shp_path = os.path.join(output_dir, shp_name)
                process_single_shapefile(
                    output_shp_path,
                    fill_ratio_threshold=FILL_RATIO_THRESHOLD,
                    convexity_threshold=CONVEXITY_THRESHOLD
                )
                print(f"  > 处理完成！结果已保存至 '{output_shp_path}'")

            except Exception as e:
                print(f"  > 处理文件 {shp_name} 时发生严重错误: {e}")

        print("\n--- 所有批次处理任务已完成 ---")