# ==============================================================================
# 程式名稱: run_path_analysis.py
# 版本: 15.2 (Pathing Clarified)
# 最後修訂日期: 2025-07-29
#
# 重要修訂事項:
# - v15.2:
#   - [修正] 修正設定檔鍵名，清晰地區分主要GIS產出與附屬報告產出路徑。
# ==============================================================================
import os
import re
import json
import sys
from dotenv import load_dotenv
from osgeo import ogr, gdal
import requests
import pandas as pd
import numpy as np
from pyproj import Transformer
import traceback
from pathlib import Path

def find_project_root() -> Path:
    """從當前腳本執行位置向上追溯，直到找到包含 'project_config.json' 的目錄。"""
    current_path = Path.cwd()
    while current_path != current_path.parent:
        if (current_path / "project_config.json").exists():
            return current_path
        current_path = current_path.parent
    if (current_path / "project_config.json").exists():
        return current_path
    raise FileNotFoundError("無法在當前目錄或其任何父目錄中找到 'project_config.json'。")

def setup_output_folder(path):
    """如果輸出資料夾不存在，則創建它。"""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"已創建輸出資料夾: {path}")

def transform_coords(points, src_epsg, dest_epsg):
    """批次轉換座標系統。"""
    transformer = Transformer.from_crs(src_epsg, dest_epsg, always_xy=True)
    return [transformer.transform(p[0], p[1]) for p in points]

def get_route_geojson(start_coord_wgs84, end_coord_wgs84, api_key):
    """獲取單一路線的 GeoJSON。"""
    headers = {'Authorization': api_key, 'Content-Type': 'application/json'}
    body = {"coordinates": [start_coord_wgs84, end_coord_wgs84]}
    try:
        response = requests.post('https://api.openrouteservice.org/v2/directions/driving-car/geojson', json=body, headers=headers, timeout=30)
        response.raise_for_status()
        route_data = response.json()
        route_data['crs'] = {"type": "name", "properties": {"name": "EPSG:4326"}}
        return route_data
    except requests.exceptions.RequestException as e:
        print(f"      警告：無法獲取路線 GeoJSON。原因: {e}"); return None

def main():
    try:
        gdal.UseExceptions(); ogr.UseExceptions()
        print("--- 開始執行【路徑分析平台 (已整合設定)】---")

        PROJECT_ROOT = find_project_root()
        with open(PROJECT_ROOT / "project_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        gis_config = config['gis_processing']
        path_config = gis_config['tasks']['run_path_analysis']

    except (FileNotFoundError, KeyError) as e:
        print(f"錯誤：讀取設定檔失敗。 {e}"); sys.exit(1)

    if not gis_config.get('enabled', False) or not path_config.get('enabled', False):
        print("--- 路徑分析任務已禁用，跳過。 ---")
        return

    # [關鍵修正] 清晰地定義兩類輸出路徑
    ancillary_output_dir = PROJECT_ROOT / path_config.get('ancillary_output_folder')
    facility_points_file_path = PROJECT_ROOT / path_config.get('facility_points_file')
    api_key_var = path_config.get('api_key_env_variable', 'ORS_API_KEY')
    skip_keyword = path_config.get('skip_keyword', '無')
    generate_geojson = path_config.get('generate_geojson_routes', True)
    
    setup_output_folder(ancillary_output_dir)

    load_dotenv(dotenv_path=PROJECT_ROOT / '.env')
    api_key = os.getenv(api_key_var)
    if not api_key:
        print(f"\n!!! 嚴重錯誤：找不到環境變數 '{api_key_var}'！請檢查您的 .env 檔案。"); sys.exit(1)

    print(f"正在讀取設施點檔案: {facility_points_file_path}")
    if not os.path.exists(facility_points_file_path):
        print(f"錯誤：找不到指定的設施點檔案: {facility_points_file_path}"); sys.exit(1)
    
    if str(facility_points_file_path).lower().endswith('.csv'):
        facility_df = pd.read_csv(facility_points_file_path, encoding='utf-8-sig')
    else:
        facility_df = pd.read_json(facility_points_file_path)

    for task in path_config.get('analysis_targets', []):
        task_name = task.get('task_name', '未命名任務')
        print(f"\n--- 開始執行路徑分析子任務: {task_name} ---")
        
        shp_path = PROJECT_ROOT / task.get('source_file')
        fields_map = task.get('fields', {})
        output_prefix = task_name.replace(' ', '_')
        
        if not shp_path.exists() or not all(k in fields_map for k in ['land_id', 'centroid_x', 'centroid_y']):
            print(f"   警告：任務 '{task_name}' 設定不完整 (找不到 source_file '{shp_path}' 或 fields 中的必要欄位)，跳過。"); continue

        print(f"   正在讀取目標圖層: {shp_path}")
        driver = ogr.GetDriverByName('ESRI Shapefile')
        dataSource_read = driver.Open(str(shp_path), 0)
        if dataSource_read is None: print(f"   警告：無法開啟 SHP 檔案 {shp_path}，跳過。"); continue
        layer_read = dataSource_read.GetLayer()
        
        feature_data_list = [{'land_id': f.GetField(fields_map['land_id']), 
                              'Centroid_X': f.GetField(fields_map['centroid_x']), 
                              'Centroid_Y': f.GetField(fields_map['centroid_y']), 
                              '_internal_fid': f.GetFID()} for f in layer_read]
        report_df = pd.DataFrame(feature_data_list)
        dest_points_twd97 = [[d['Centroid_X'], d['Centroid_Y']] for d in feature_data_list]
        dest_points_wgs84 = transform_coords(dest_points_twd97, 'EPSG:3826', 'EPSG:4326')
        dataSource_read = None

        print("   正在準備 SHP 檔案結構以進行更新...")
        fields_to_create = {}
        for _, sp_row in facility_df.iterrows():
            eng_cat = sp_row.get('eng_category')
            if eng_cat:
                eng_cat_short = re.sub(r'[^a-zA-Z0-9]', '', eng_cat)[:7]
                fields_to_create[eng_cat] = {"name_field": f"{eng_cat_short}_Na", "dist_field": f"{eng_cat_short}_D"}
        
        dataSource_update = driver.Open(str(shp_path), 1)
        layer_update = dataSource_update.GetLayer()
        layer_defn = layer_update.GetLayerDefn()
        existing_fields = [layer_defn.GetFieldDefn(i).GetName() for i in range(layer_defn.GetFieldCount())]
        
        for cat, field_pair in fields_to_create.items():
            if field_pair['name_field'] not in existing_fields:
                name_field_defn = ogr.FieldDefn(field_pair['name_field'], ogr.OFTString); name_field_defn.SetWidth(50); layer_update.CreateField(name_field_defn); print(f"     已新增欄位: {field_pair['name_field']}")
            if field_pair['dist_field'] not in existing_fields:
                dist_field_defn = ogr.FieldDefn(field_pair['dist_field'], ogr.OFTReal); dist_field_defn.SetWidth(12); dist_field_defn.SetPrecision(2); layer_update.CreateField(dist_field_defn); print(f"     已新增欄位: {field_pair['dist_field']}")
        dataSource_update = None
        
        print("   開始遍歷設施點進行計算...")
        for _, facility_row in facility_df.iterrows():
            # ... (遍歷設施點的內部邏輯與上一版相同) ...
            name, eng_category = facility_row.get("name"), facility_row.get("eng_category")
            if pd.isna(name): continue

            if name == skip_keyword:
                print(f"     處理佔位符: {facility_row.get('category')} - {name}")
                if eng_category and fields_to_create.get(eng_category):
                    report_df[fields_to_create[eng_category]['dist_field']] = np.nan
                continue
            
            # ... (座標轉換與API請求邏輯不變) ...
            
            # [關鍵修正] 確保在迴圈內部重新打開檔案進行更新
            dataSource_update_loop = driver.Open(str(shp_path), 1)
            layer_update_loop = dataSource_update_loop.GetLayer()
            # ... (SetField邏輯不變) ...
            dataSource_update_loop = None # 確保每次更新後都關閉檔案

            if generate_geojson:
                # ... (GeoJSON生成邏輯不變) ...
                if route_geojson:
                    geojson_filename = ancillary_output_dir / f"{output_prefix}_route_{eng_category}.geojson"
                    with open(geojson_filename, 'w', encoding='utf-8') as f: json.dump(route_geojson, f, ensure_ascii=False, indent=2)

        print(f"   正在為任務 '{task_name}' 產生最終報表...")
        report_df.drop(columns=['_internal_fid'], inplace=True, errors='ignore')
        
        report_xlsx = ancillary_output_dir / f"{output_prefix}_report.xlsx"
        report_json = ancillary_output_dir / f"{output_prefix}_report.json"
        
        with pd.ExcelWriter(report_xlsx, engine='openpyxl') as writer:
            report_df.to_excel(writer, sheet_name='Distance_Analysis', index=False)
            facility_df.to_excel(writer, sheet_name='Facility_Points_List', index=False)
        
        report_df.to_json(report_json, orient='records', indent=2, force_ascii=False)
        print(f"   ✅ Shapefile '{shp_path.name}' 已在原位置更新。")
        print(f"   ✅ 附屬報告已儲存至 '{ancillary_output_dir}'。")

    print("\n--- 所有已啟用的路徑分析任務執行完畢！ ---")

if __name__ == '__main__':
    main()