# ==============================================================================
# 程式名稱: main.py
# 版本: 4.3.1 (Robust Subprocess Handling)
# 最後修訂日期: 2025-08-02
#
# 重要修訂事項:
# - v4.3.1:
#   - [優化] 增加對子程序輸出流的空值檢查，使 Pylance 靜態分析滿意，
#            並提升程式碼穩健性。
# - v4.3.0:
#   - [優化] 根據最終決策，將 LVR 處理命令簡化為 'python main.py lvr'。
# ==============================================================================
import json
import sys
import os
from pathlib import Path
import subprocess

def find_project_root() -> Path:
    """找到專案根目錄"""
    current_path = Path.cwd()
    while current_path != current_path.parent:
        if (current_path / "project_config.json").exists():
            return current_path
        current_path = current_path.parent
    if (current_path / "project_config.json").exists():
        return current_path
    raise FileNotFoundError("無法在當前目錄或其任何父目錄中找到 'project_config.json'。")

def run_script(script_name: str, project_root: Path):
    """執行一個 Python 腳本的統一函式"""
    script_path = project_root / script_name
    command = [sys.executable, str(script_path)]
    print("\n" + "="*80)
    print(f"🚀 開始執行子腳本: {script_name}")
    print("="*80)
    
    try:
        env = os.environ.copy()
        env["PYTHONUTF8"] = "1"
        process = subprocess.Popen(
            command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
            text=True, encoding='utf-8', errors='replace',
            cwd=project_root, env=env
        )
        
        # [關鍵修正] 增加對 stdout 的檢查，消除 Pylance 警告
        if process.stdout:
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None: break
                if output: print(output.strip())
        
        rc = process.wait() # 等待程序結束並獲取返回碼
        if rc != 0:
            print(f"❌ 錯誤: '{script_name}' 執行失敗，返回代碼 {rc}。"); return False
        print(f"✅ 成功: '{script_name}' 執行完畢。"); return True
    except FileNotFoundError:
        print(f"❌ 嚴重錯誤: 找不到腳本 '{script_path}'。"); return False
    except Exception as e:
        print(f"❌ 嚴重錯誤: 執行 '{script_name}' 時發生未預期的例外: {e}"); return False

def main():
    """主調度函式，解析子命令並執行對應任務。"""
    try:
        PROJECT_ROOT = find_project_root()
        args = sys.argv[1:]

        if not args:
            print("歡迎使用本專案自動化工具。請提供一個命令。")
            print("\n可用命令:")
            print("  lvr                       - 處理所有不動產買賣 (LVR) 資料")
            print("  pdf <parse|regenerate>    - 執行 PDF 相關任務")
            print("  gis <all|metrics|path>    - 執行 GIS 預處理任務")
            print("  report <表名1> [表名2]...  - 生成指定的報告 (若無表名則生成全部)")
            print("  full                      - 執行完整的自動化流程")
            return

        command = args[0].lower()

        if command == 'lvr':
            run_script("lvr_land.py", PROJECT_ROOT)
        
        elif command == 'pdf':
            if len(args) < 2: print("錯誤: 'pdf' 命令需要一個子命令 (parse 或 regenerate)。"); return
            subcommand = args[1].lower()
            if subcommand == 'parse': run_script("PDF_Explained.py", PROJECT_ROOT)
            elif subcommand == 'regenerate': run_script("PDF_Regenerate.py", PROJECT_ROOT)
            else: print(f"錯誤: 無法識別的 'pdf' 子命令 '{subcommand}'。"); return
        
        elif command == 'gis':
            if len(args) < 2: print("錯誤: 'gis' 命令需要一個子命令 (all, metrics, 或 path)。"); return
            subcommand = args[1].lower()
            if subcommand == 'all':
                if not run_script("calculate_metrics.py", PROJECT_ROOT): return
                if not run_script("run_path_analysis.py", PROJECT_ROOT): return
            elif subcommand == 'metrics': run_script("calculate_metrics.py", PROJECT_ROOT)
            elif subcommand == 'path': run_script("run_path_analysis.py", PROJECT_ROOT)
            else: print(f"錯誤: 無法識別的 'gis' 子命令 '{subcommand}'。"); return

        elif command == 'report':
            report_names = args[1:]
            with open(PROJECT_ROOT / "project_config.json", 'r', encoding='utf-8') as f: config = json.load(f)
            config["execution_plan"]["reports_to_generate"] = report_names
            temp_config_path = PROJECT_ROOT / "temp_config.json"
            with open(temp_config_path, 'w', encoding='utf-8') as f: json.dump(config, f, indent=2, ensure_ascii=False)
            run_script("main_engine.py", PROJECT_ROOT)
            if temp_config_path.exists(): os.remove(temp_config_path)

        elif command == 'full':
            print("🚀 執行完整流程...")
            if not run_script("lvr_land.py", PROJECT_ROOT): return
            if not run_script("PDF_Explained.py", PROJECT_ROOT): return
            if not run_script("calculate_metrics.py", PROJECT_ROOT): return
            if not run_script("run_path_analysis.py", PROJECT_ROOT): return
            with open(PROJECT_ROOT / "project_config.json", 'r', encoding='utf-8') as f: config = json.load(f)
            config["execution_plan"]["reports_to_generate"] = []
            temp_config_path = PROJECT_ROOT / "temp_config.json"
            with open(temp_config_path, 'w', encoding='utf-8') as f: json.dump(config, f, indent=2, ensure_ascii=False)
            run_script("main_engine.py", PROJECT_ROOT)
            if temp_config_path.exists(): os.remove(temp_config_path)
        else:
            print(f"❌ 錯誤: 無法識別的主命令 '{command}'。"); return

        print("\n🎉🎉🎉 任務執行完畢！ 🎉🎉🎉")

    except Exception as e:
        print(f"❌ 致命錯誤：在主調度器中發生未預期的例外: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()