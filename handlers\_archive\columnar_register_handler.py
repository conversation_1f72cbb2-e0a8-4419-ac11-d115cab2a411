# ===================================================================
#  檔案名稱: handlers/columnar_register_handler.py
#  版    本: v3.6 - The Definitive Fix
#  說    明: 「欄位式」清冊處理器，經過最終除錯。
#            - [重構] 徹底重寫 process 函式，採用「藍圖讀取、預先施工、統一填寫」的
#                     三階段分離邏輯，從根源上解決所有合併儲存格及資料不完整的 bug。
# ===================================================================
import pandas as pd
import openpyxl
from openpyxl.utils.cell import coordinate_from_string
from openpyxl.utils import get_column_letter, column_index_from_string
from pathlib import Path
from openpyxl.worksheet.worksheet import Worksheet
import re

def get_rows_to_validate(df: pd.DataFrame):
    if 'Validation' not in df.columns or df['Validation'].isnull().all():
        return df
    return df[df['Validation'].astype(str).str.lower() != 'skip'].copy()

def validate_mapping_config(map_dfs: list[pd.DataFrame], gdf: pd.DataFrame, p_info: dict, m_path: Path):
    available_fields = set(gdf.columns).union(set(p_info.keys()))
    all_missing_fields = set()
    for df in map_dfs:
        if 'Source Field' in df.columns:
            active_df = get_rows_to_validate(df)
            source_fields = set(active_df['Source Field'].dropna().astype(str))
            all_missing_fields.update(source_fields - available_fields)
    if all_missing_fields:
        print(f"-> 錯誤(來自 {m_path.name}): 找不到 Source Field: {', '.join(sorted(list(all_missing_fields)))}")
        print(f"  可用的欄位有: {', '.join(sorted(list(available_fields)))}")
        return False
    return True

def process(project_info: dict, template_path: Path, layout_config: dict, layout_path: Path, mapping_path: Path, gdf: pd.DataFrame, output_dir: Path):
    print(f"--- 使用策略: 'columnar_register_handler' v3.6 (決定性修正版) ---")
    
    try:
        static_map_df = pd.read_excel(mapping_path, sheet_name='靜態資料對應', engine='openpyxl')
        dynamic_map_df = pd.read_excel(mapping_path, sheet_name='動態資料對應', engine='openpyxl')
    except Exception as e:
        print(f"-> 錯誤：讀取 mapping 檔案 '{mapping_path.name}' 失敗: {e}")
        return

    if not validate_mapping_config([static_map_df, dynamic_map_df], gdf, project_info, mapping_path):
        print(f"-> 因 mapping 設定檔驗證失敗，已中止。")
        return

    workbook = openpyxl.load_workbook(template_path)
    active_sheet = workbook.active
    if not isinstance(active_sheet, Worksheet):
        print("-> 錯誤：範本檔案中找不到有效的工作表。"); return
    sheet: Worksheet = active_sheet
    
    # 1. 填寫靜態資料
    for _, row in static_map_df.iterrows():
        cell_coord, field = row.get('Target Cell'), row.get('Source Field')
        if pd.notna(cell_coord) and isinstance(field, str) and field in project_info:
            sheet[str(cell_coord)] = project_info.get(field)

    if 'Target Cell' not in dynamic_map_df.columns or dynamic_map_df.empty:
        print("-> 錯誤：動態資料對應表中缺少 'Target Cell' 或沒有內容。")
        return
        
    first_cell = str(dynamic_map_df['Target Cell'].iloc[0])
    if not re.match(r"^[A-Z]+[0-9]+$", first_cell, re.I):
        print(f"-> 致命錯誤(來自 mapping 檔)：動態資料對應表的第一個 Target Cell '{first_cell}' 不是合法的 Excel 座標。")
        return
        
    template_col_str, _ = coordinate_from_string(first_cell)
    template_col_idx = column_index_from_string(template_col_str)

    # --- [V3.6 核心重構] ---
    # 步驟 1: 讀取範本欄位的「藍圖」
    template_blueprint = {
        "width": sheet.column_dimensions[template_col_str].width,
        "styles": {r: sheet.cell(row=r, column=template_col_idx).style for r in range(1, sheet.max_row + 1)},
        "merged_ranges": [m for m in sheet.merged_cells.ranges if m.min_col <= template_col_idx <= m.max_col]
    }

    gdf_reset = gdf.reset_index(drop=True)

    # 步驟 2: 預先施工 - 根據藍圖，一次性複製所有欄位的格式
    for idx in range(len(gdf_reset)):
        target_col_idx = template_col_idx + idx
        target_col_letter = get_column_letter(target_col_idx)
        
        if template_blueprint["width"]:
            sheet.column_dimensions[target_col_letter].width = template_blueprint["width"]

        for r, style in template_blueprint["styles"].items():
            sheet.cell(row=r, column=target_col_idx).style = style
        
        for merged_range in template_blueprint["merged_ranges"]:
            col_offset = target_col_idx - template_col_idx
            new_min_col, new_max_col = merged_range.min_col + col_offset, merged_range.max_col + col_offset
            sheet.merge_cells(start_row=merged_range.min_row, start_column=new_min_col,
                              end_row=merged_range.max_row, end_column=new_max_col)

    # 步驟 3: 統一裝修 - 獨立、完整地填寫所有資料
    for idx, data_row in gdf_reset.iterrows():
        target_col_idx = template_col_idx + idx
        for _, map_row in dynamic_map_df.iterrows():
            source_field, template_cell_str = map_row.get('Source Field'), map_row.get('Target Cell')
            if pd.notna(template_cell_str) and isinstance(source_field, str) and source_field in data_row and pd.notna(data_row[source_field]):
                _, row_num = coordinate_from_string(str(template_cell_str))
                sheet.cell(row=row_num, column=target_col_idx).value = data_row[source_field]
    
    # 步驟 4: 設定列印範圍
    if not gdf.empty:
        final_col_idx = template_col_idx + len(gdf) - 1
        final_col_letter = get_column_letter(final_col_idx)
        max_row = sheet.max_row
        sheet.print_area = f'A1:{final_col_letter}{max_row}'
    
    base_name = template_path.stem.replace('_template', '')
    output_filename = f"{base_name}_完整報告.xlsx"
    output_path = output_dir / output_filename
    workbook.save(output_path)
    print(f"-> 成功生成欄位式清冊報告: {output_filename}")