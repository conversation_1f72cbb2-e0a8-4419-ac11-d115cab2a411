# ===================================================================
#  檔案名稱: handlers/register_block_handler.py
#  版    本: v3.3 - Dimension Fix
#  說    明: 修正 v3.2 中因獲取欄寬導致的 TypeError。
# ===================================================================
import pandas as pd
import openpyxl
from openpyxl.utils import get_column_letter, column_index_from_string
from openpyxl.utils.cell import coordinate_from_string
from openpyxl.worksheet.worksheet import Worksheet
import math
from typing import Dict, Any
import logging

from .base import BaseHandler, HandlerContext
from . import utils

class RegisterBlockHandler(BaseHandler):
    def process(self, context: HandlerContext) -> bool:
        logger = context.logger
        logger.info(f"--- 使用策略: 'RegisterBlockHandler' v3.3 (Dimension Fix) ---")
        
        sheet = context.sheet
        layout_config = context.layout_config
        mapping_path = context.mapping_path
        gdfs = context.gdfs
        project_info = context.project_info

        if mapping_path is None or not mapping_path.exists():
            logger.error(f"-> 致命錯誤: 找不到 mapping 檔案 '{mapping_path}'")
            return False

        source_key = str(layout_config.get('data_source_key'))
        if not source_key or source_key not in gdfs:
            logger.error(f"-> 致命錯誤: layout 中未指定有效的 'data_source_key'。"); return False
        
        gdf = gdfs[source_key]
        logger.info(f"-> 已選用資料來源: '{source_key}' (共 {len(gdf)} 筆資料)")
        
        try:
            s_map = pd.read_excel(mapping_path, sheet_name=layout_config.get('static_data_sheet', '靜態資料對應'), engine='openpyxl')
            d_map = pd.read_excel(mapping_path, sheet_name=layout_config.get('dynamic_data_sheet', '動態資料對應'), engine='openpyxl')
        except Exception as e:
            logger.error(f"-> 錯誤：讀取 mapping 檔案 '{mapping_path.name}' 的工作表失敗: {e}"); return False

        records_per_block = int(layout_config.get('records_per_block', 7))
        block_height = int(layout_config.get('template_block_height', 36))
        start_col_letter = str(layout_config.get('data_start_column', 'D'))
        start_col_idx = column_index_from_string(start_col_letter)
        
        total_records = len(gdf)
        total_pages = math.ceil(total_records / records_per_block)
        gdf_reset = gdf.reset_index(drop=True)

        merged_blueprint = [mr for mr in sheet.merged_cells.ranges if mr.min_row <= block_height]
        warned_fields = set()

        # [修正] 預先儲存範本的行高和欄寬
        template_row_heights = {i: dim.height for i, dim in sheet.row_dimensions.items() if i <= block_height}
        # [關鍵修正] 直接使用欄位字母作為鍵
        template_col_widths = {key: dim.width for key, dim in sheet.column_dimensions.items()}

        for page_num in range(total_pages):
            row_offset = page_num * block_height
            
            if page_num > 0:
                # 複製範本區塊的樣式和內容
                for r_idx in range(1, block_height + 1):
                    if r_idx in template_row_heights:
                        sheet.row_dimensions[r_idx + row_offset].height = template_row_heights[r_idx]

                    for c_idx in range(1, sheet.max_column + 1):
                        source_cell = sheet.cell(row=r_idx, column=c_idx)
                        if not source_cell.has_style and not source_cell.value: continue
                        target_cell = sheet.cell(row=r_idx + row_offset, column=c_idx)
                        if source_cell.has_style:
                            target_cell.font = source_cell.font.copy()
                            target_cell.border = source_cell.border.copy()
                            target_cell.fill = source_cell.fill.copy()
                            target_cell.number_format = source_cell.number_format
                            target_cell.protection = source_cell.protection.copy()
                            target_cell.alignment = source_cell.alignment.copy()
                        if source_cell.value:
                            utils.write_to_cell(sheet, target_cell.row, target_cell.column, source_cell.value, logger)
                
                # 複製合併儲存格
                for template_range in merged_blueprint:
                    sheet.merge_cells(start_row=template_range.min_row + row_offset, start_column=template_range.min_col,
                                      end_row=template_range.max_row + row_offset, end_column=template_range.max_col)
        
        # [修正] 在複製完所有頁面後，再統一設定欄寬
        for col, width in template_col_widths.items():
            if width:
                sheet.column_dimensions[col].width = width

        # --- 開始填寫資料 ---
        for page_num in range(total_pages):
            row_offset = page_num * block_height

            # 填寫靜態資料
            for _, row in s_map.iterrows():
                cell_coord, field = row.get('Target Cell'), row.get('Source Field')
                if pd.notna(cell_coord) and isinstance(field, str):
                    if field in project_info:
                        try:
                            col_str, r_num = coordinate_from_string(str(cell_coord))
                            utils.write_to_cell(sheet, r_num + row_offset, column_index_from_string(col_str), project_info.get(field), logger)
                        except (ValueError, TypeError): logger.warning(f"   - 警告: 靜態 mapping 中發現無效座標 '{cell_coord}'")
                    elif field not in warned_fields:
                        logger.warning(f"   - 欄位不匹配: 靜態 mapping 中的 '{field}' 在 project_config.json 中找不到。")
                        warned_fields.add(field)

            # 填寫動態資料
            start_record_idx = page_num * records_per_block
            end_record_idx = min((page_num + 1) * records_per_block, total_records)
            
            for record_idx_on_page, record_abs_idx in enumerate(range(start_record_idx, end_record_idx)):
                target_col_idx = start_col_idx + record_idx_on_page
                data_row = gdf_reset.iloc[record_abs_idx]
                for _, map_row in d_map.iterrows():
                    source_field, template_cell_str = map_row.get('Source Field'), map_row.get('Target Cell')
                    if pd.notna(template_cell_str) and isinstance(source_field, str):
                        if source_field in data_row and pd.notna(data_row[source_field]):
                            try:
                                _, r_num = coordinate_from_string(str(template_cell_str))
                                utils.write_to_cell(sheet, r_num + row_offset, target_col_idx, data_row[source_field], logger)
                            except (ValueError, TypeError): logger.warning(f"   - 警告: 動態 mapping 中發現無效座標 '{template_cell_str}'")
                        elif source_field not in warned_fields:
                            logger.warning(f"   - 欄位不匹配: 動態 mapping 中的 '{source_field}' 在 GIS 資料中找不到。")
                            warned_fields.add(source_field)
        
        # 清除最後一頁多餘的資料
        records_in_last_page = total_records % records_per_block
        if records_in_last_page > 0:
            last_page_offset = (total_pages - 1) * block_height
            for col_to_clear_idx in range(start_col_idx + records_in_last_page, start_col_idx + records_per_block):
                for _, map_row in d_map.iterrows():
                    template_cell_str = map_row.get('Target Cell')
                    if pd.notna(template_cell_str):
                        try:
                            _, r_num = coordinate_from_string(str(template_cell_str))
                            utils.write_to_cell(sheet, r_num + last_page_offset, col_to_clear_idx, None, logger)
                        except (ValueError, TypeError):
                            pass

        # 設定列印區域
        max_print_row = total_pages * block_height if total_pages > 0 else block_height
        sheet.print_area = f'A1:{get_column_letter(sheet.max_column)}{max_print_row}'
        
        if layout_config.get('print_title_rows'):
            sheet.print_title_rows = str(layout_config.get('print_title_rows'))
        
        return True