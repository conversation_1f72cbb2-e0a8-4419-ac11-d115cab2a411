# ===================================================================
#  檔案名稱: handlers/register_block_handler.py
#  版    本: v3.1 - Import Fix
#  說    明: 修正 openpyxl 的 import 路徑問題。
# ===================================================================
import pandas as pd
import openpyxl
from openpyxl.utils import get_column_letter, column_index_from_string
from openpyxl.utils.cell import coordinate_from_string
from openpyxl.worksheet.worksheet import Worksheet
import math
from typing import Dict, Any
import logging

from .base import BaseHandler, HandlerContext
from . import utils

class RegisterBlockHandler(BaseHandler):
    def process(self, context: HandlerContext) -> bool:
        logger = context.logger
        logger.info(f"--- 使用策略: 'RegisterBlockHandler' v3.1 (Import Fix) ---")
        
        sheet = context.sheet
        layout_config = context.layout_config
        mapping_path = context.mapping_path
        gdfs = context.gdfs
        project_info = context.project_info

        source_key = layout_config.get('data_source_key')
        if not source_key or source_key not in gdfs:
            logger.error(f"-> 致命錯誤: layout 中未指定有效的 'data_source_key'。"); return False
        
        gdf = gdfs[source_key]
        logger.info(f"-> 已選用資料來源: '{source_key}' (共 {len(gdf)} 筆資料)")
        
        try:
            s_map = pd.read_excel(mapping_path, sheet_name=layout_config.get('static_data_sheet', '靜態資料對應'), engine='openpyxl')
            d_map = pd.read_excel(mapping_path, sheet_name=layout_config.get('dynamic_data_sheet', '動態資料對應'), engine='openpyxl')
        except Exception as e:
            logger.error(f"-> 錯誤：讀取 mapping 檔案 '{mapping_path.name}' 的工作表失敗: {e}"); return False

        records_per_block = int(layout_config.get('records_per_block', 7))
        block_height = int(layout_config.get('template_block_height', 36))
        start_col_letter = layout_config.get('data_start_column', 'D')
        start_col_idx = column_index_from_string(start_col_letter)
        
        total_records = len(gdf)
        total_pages = math.ceil(total_records / records_per_block)
        gdf_reset = gdf.reset_index(drop=True)

        merged_blueprint = [mr for mr in sheet.merged_cells.ranges if mr.min_row <= block_height]
        warned_fields = set()

        for page_num in range(total_pages):
            row_offset = page_num * block_height
            
            if page_num > 0:
                for r_idx in range(1, block_height + 1):
                    for c_idx in range(1, sheet.max_column + 1):
                        source_cell = sheet.cell(row=r_idx, column=c_idx)
                        if not source_cell.has_style and not source_cell.value: continue
                        target_cell = sheet.cell(row=r_idx + row_offset, column=c_idx)
                        if source_cell.has_style:
                            target_cell.font, target_cell.border, target_cell.fill, target_cell.number_format, target_cell.protection, target_cell.alignment = source_cell.font.copy(), source_cell.border.copy(), source_cell.fill.copy(), source_cell.number_format, source_cell.protection.copy(), source_cell.alignment.copy()
                        if source_cell.value:
                            utils.write_to_cell(sheet, row=target_cell.row, col=target_cell.column, value=source_cell.value, logger=logger)
                
                for template_range in merged_blueprint:
                    sheet.merge_cells(start_row=template_range.min_row + row_offset, start_column=template_range.min_col, end_row=template_range.max_row + row_offset, end_column=template_range.max_col)

            for _, row in s_map.iterrows():
                cell_coord, field = row.get('Target Cell'), row.get('Source Field')
                if pd.notna(cell_coord) and isinstance(field, str):
                    if field in project_info:
                        try:
                            col_str, r_num = coordinate_from_string(str(cell_coord))
                            utils.write_to_cell(sheet, row=r_num + row_offset, col=column_index_from_string(col_str), value=project_info.get(field), logger=logger)
                        except (ValueError, TypeError): logger.warning(f"   - 警告: 靜態 mapping 中發現無效座標 '{cell_coord}'")
                    elif field not in warned_fields:
                        logger.warning(f"   - 欄位不匹配: 靜態 mapping 中的 '{field}' 在 project_config.json 中找不到。")
                        warned_fields.add(field)

            start_record_idx, end_record_idx = page_num * records_per_block, min((page_num + 1) * records_per_block, total_records)
            for record_idx_on_page, record_abs_idx in enumerate(range(start_record_idx, end_record_idx)):
                target_col_idx = start_col_idx + record_idx_on_page
                data_row = gdf_reset.iloc[record_abs_idx]
                for _, map_row in d_map.iterrows():
                    source_field, template_cell_str = map_row.get('Source Field'), map_row.get('Target Cell')
                    if pd.notna(template_cell_str) and isinstance(source_field, str):
                        if source_field in data_row and pd.notna(data_row[source_field]):
                            try:
                                _, r_num = coordinate_from_string(str(template_cell_str))
                                utils.write_to_cell(sheet, row=r_num + row_offset, col=target_col_idx, value=data_row[source_field], logger=logger)
                            except (ValueError, TypeError): logger.warning(f"   - 警告: 動態 mapping 中發現無效座標 '{template_cell_str}'")
                        elif source_field not in warned_fields:
                            logger.warning(f"   - 欄位不匹配: 動態 mapping 中的 '{source_field}' 在 GIS 資料中找不到。")
                            warned_fields.add(source_field)

        max_print_row = total_pages * block_height if total_pages > 0 else block_height
        cols_in_last_block = total_records % records_per_block
        if cols_in_last_block == 0 and total_records > 0: cols_in_last_block = records_per_block
        last_col_idx = start_col_idx + cols_in_last_block - 1 if total_records > 0 else start_col_idx
        last_col_letter = get_column_letter(last_col_idx)
        sheet.print_area = f'A1:{last_col_letter}{max_print_row}'
        
        if layout_config.get('print_title_rows'):
            sheet.print_title_rows = str(layout_config.get('print_title_rows'))
        
        return True