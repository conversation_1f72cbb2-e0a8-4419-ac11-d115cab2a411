# ===================================================================
#  檔案名稱: handlers/base.py
#  版    本: v1.1
#  說    明: 在 HandlerContext 中新增 template_path 和 base_name 屬性。
# ===================================================================
import pandas as pd
import logging
from typing import Optional, Any, Dict
from pathlib import Path
from openpyxl.worksheet.worksheet import Worksheet
from abc import ABC, abstractmethod
from dataclasses import dataclass, field

@dataclass
class HandlerContext:
    """
    Handler 的執行上下文，作為引擎與 Handler 之間的標準資料傳遞物件。
    """
    # --- 通用屬性 (所有模式通用) ---
    sheet: Worksheet
    layout_config: Dict[str, Any]
    gdfs: Dict[str, pd.DataFrame]
    gis_sources_config: Dict[str, Any]
    project_info: Dict[str, Any]
    logger: logging.Logger
    base_dir: Path
    template_path: Path  # <-- [關鍵修正] 新增
    base_name: str       # <-- [關鍵修正] 新增

    # --- 路徑屬性 (依需傳入) ---
    mapping_path: Optional[Path] = None
    selection_path: Optional[Path] = None
    rulebook_path: Optional[Path] = None
    master_plan_path: Optional[Path] = None
    matrix_rulebook_path: Optional[Path] = None

    # --- 模式特定屬性 (依模式傳入) ---
    group_df: Optional[pd.DataFrame] = None
    target_id: Optional[str] = None

class BaseHandler(ABC):
    """
    所有 Handler 的抽象基底類別。
    """
    @abstractmethod
    def process(self, context: HandlerContext) -> bool:
        """
        處理單一工作表的核心邏輯。
        """
        pass