# ===================================================================
#  檔案名稱: main_engine.py
#  版    本: v10.8 - Descriptive Sheet Names
#  說    明: 強化頁簽命名邏輯，採 "組{Group_ID}_比準地{Subject_ID}" 格式。
# ===================================================================
import pandas as pd
from pathlib import Path
import json, traceback, logging, argparse, openpyxl, sys
from shutil import copyfile
from typing import Optional, Dict, Any
import re 

# 將 handlers 目錄加入系統路徑 (如果直接執行此檔案)
try:
    BASE_DIR = Path(__file__).resolve().parent
except NameError:
    BASE_DIR = Path.cwd()
sys.path.append(str(BASE_DIR))

# 引入核心基底類別和上下文物件
from handlers.base import HandlerContext, BaseHandler

# 配置日誌系統
def setup_logging(log_dir: Path, task_name: str, to_file: bool = True) -> logging.Logger:
    log_dir.mkdir(parents=True, exist_ok=True)
    log_filename = log_dir / f"{task_name}_執行日誌.log"
    logger = logging.getLogger(task_name); logger.setLevel(logging.INFO)
    if logger.hasHandlers(): logger.handlers.clear()
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    stream_handler = logging.StreamHandler(); stream_handler.setFormatter(logging.Formatter('%(message)s'))
    logger.addHandler(stream_handler)

    if to_file:
        file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8'); file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    return logger

# 從各自的檔案中匯入 Handler 類別
from handlers.register_block_handler import RegisterBlockHandler
from handlers.comparison_form_handler import ComparisonFormHandler
from handlers.comparison_matrix_handler import ComparisonMatrixHandler
from handlers.dynamic_form_handler import DynamicFormHandler

# 將 Handler 名稱字串映射到實際的 Handler 類別
HANDLER_CLASS_MAP: Dict[str, type[BaseHandler]] = {
    'register_block_handler': RegisterBlockHandler,
    'comparison_form_handler': ComparisonFormHandler,
    'comparison_matrix_handler': ComparisonMatrixHandler,
    'dynamic_form_handler': DynamicFormHandler,
}

def get_handler_instance(handler_name: str, logger: logging.Logger) -> Optional[BaseHandler]:
    """根據 Handler 名稱字串，返回對應的 Handler 類別實例。"""
    handler_class = HANDLER_CLASS_MAP.get(handler_name)
    if handler_class:
        return handler_class()
    logger.error(f"錯誤：在 HANDLER_CLASS_MAP 中找不到名為 '{handler_name}' 的 Handler。")
    return None

def main():
    print(f"=== 土地徵收報表自動化引擎 v10.8 (Descriptive Sheet Names) ===")
    global BASE_DIR
    
    global_logger = setup_logging(Path("temp_log"), "global_init", to_file=False)
    
    parser = argparse.ArgumentParser(description="土地徵收報表自動化引擎")
    parser.add_argument('task', nargs='?', default=None, help="要執行的任務名稱 (省略 '_template.xlsx' 後綴)")
    parser.add_argument('--target', default=None, help="要處理的特定 ID 或組別 ID，或使用 'ALL'")
    args = parser.parse_args()

    try:
        if not BASE_DIR.exists():
            BASE_DIR = Path.cwd()

        config_path = BASE_DIR / "form_input" / "config" / "project_config.json"
        with open(config_path, 'r', encoding='utf-8') as f: project_config = json.load(f)
        
        project_info = project_config.get("project_info", {})
        paths = project_config.get("paths", {})
        gis_sources_config = paths.get("gis_data_sources", {})
        general_settings = project_config.get("general_settings", {})

        template_dir = BASE_DIR / paths.get("form_input", "form_input/") / "templates"
        output_dir = BASE_DIR / paths.get("form_results", "form_results/")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        import geopandas as gpd
        
        gdfs = {}
        for key, info in gis_sources_config.items():
            if "path" in info and (BASE_DIR / info["path"]).exists():
                gdf = gpd.read_file(BASE_DIR / info["path"], encoding='utf-8')
                if 'id_column' in info and info['id_column'] in gdf.columns:
                    gdf[info['id_column']] = gdf[info['id_column']].astype(str)
                gdfs[key] = gdf
            else:
                global_logger.warning(f"警告：找不到 GIS 檔案 '{info.get('path')}'，已跳過該資料源。")

    except Exception as e:
        print(f"-> 致命錯誤：讀取全域設定或 GIS 資料失敗: {e}"); return

    task_name_to_find = args.task.replace(" ", "_") if args.task else "*"
    tasks_to_process = list(template_dir.glob(f"{task_name_to_find}_template.xlsx"))
    if not tasks_to_process:
        print(f"-> 在範本資料夾 '{template_dir}' 中未找到任何與 '{task_name_to_find}' 匹配的任務。"); return

    for template_path in tasks_to_process:
        base_name = template_path.stem.replace('_template', '')
        logger = setup_logging(output_dir, base_name, to_file=True) 
        logger.info(f"==================================================\n開始處理任務: {base_name}")
        
        try:
            layout_config_path = template_path.with_name(f"{base_name}_layout.xlsx")
            if not layout_config_path.exists():
                logger.error(f"-> 致命錯誤: 找不到佈局設定檔 '{layout_config_path.name}'"); continue
            layout_config = pd.read_excel(layout_config_path, engine='openpyxl').set_index('Key')['Value'].to_dict()

            handler_name_str = layout_config.get('handler_module')
            if not handler_name_str:
                logger.error("-> 致命錯誤: layout 中未指定 'handler_module'"); continue
            handler_instance = get_handler_instance(str(handler_name_str), logger)
            if not handler_instance: continue

            processing_mode = str(layout_config.get('processing_mode', 'id_based'))
            logger.info(f"   - 檢測到處理模式: '{processing_mode}'")
            
            base_context_params = {
                "layout_config": layout_config, "gdfs": gdfs, "gis_sources_config": gis_sources_config,
                "project_info": project_info, "logger": logger, "base_dir": BASE_DIR,
                "template_path": template_path, "base_name": base_name 
            }
            
            if processing_mode == 'block_based':
                output_filename = output_dir / f"{base_name}_完整報告.xlsx"
                copyfile(template_path, output_filename)
                wb = openpyxl.load_workbook(output_filename)
                sheet = wb.active
                assert sheet is not None, f"工作簿 '{output_filename}' 中沒有活動工作表"
                mapping_path = template_path.with_name(f"{base_name}_mapping.xlsx")
                if not mapping_path.exists():
                    logger.warning(f"   - 注意: 區塊模式缺少 mapping 檔案 '{mapping_path.name}'，如果 Handler 需要它將會失敗。")
                context = HandlerContext(sheet=sheet, **base_context_params, mapping_path=mapping_path)
                success = handler_instance.process(context)
                if success:
                    wb.save(output_filename); logger.info(f"報告已保存至: {output_filename}")
                else:
                    logger.error("Handler 回報處理失敗，報告未完整生成或未保存。")

            elif processing_mode in ['selection_based', 'id_based']:
                output_filename = output_dir / f"{base_name}.xlsx"
                logger.info(f"將整合所有結果至單一檔案: {output_filename}")
                
                copyfile(template_path, output_filename)
                wb = openpyxl.load_workbook(output_filename)
                template_sheet = wb.active
                assert template_sheet is not None, f"範本檔案 '{template_path}' 中沒有活動工作表"
                template_sheet.title = "範本"
                
                items_to_process = []
                is_id_mode = (processing_mode == 'id_based')
                main_src_key = ""
                selection_df = pd.DataFrame()

                if is_id_mode:
                    main_src_key_from_config = layout_config.get('main_data_source_key', general_settings.get('default_sort_key'))
                    if not main_src_key_from_config or str(main_src_key_from_config) not in gdfs:
                        logger.error(f"-> 致命錯誤: id_based 模式需要在 layout 中指定有效的 'main_data_source_key'"); continue
                    main_src_key = str(main_src_key_from_config)
                    id_col = str(gis_sources_config[main_src_key]['id_column'])
                    items_to_process = gdfs[main_src_key][id_col].dropna().unique()
                    logger.info(f"在 '{main_src_key}' 中找到 {len(items_to_process)} 個獨立 ID 需要處理。")
                else: # selection_based 模式
                    selection_path = template_path.with_name(f"{base_name}_selection.xlsx")
                    if not selection_path.exists():
                        logger.error(f"-> 致命錯誤: 找不到任務專屬的比較組設定檔 '{selection_path}'。"); continue
                    selection_df = pd.read_excel(selection_path, dtype={'ID': str, 'Group_ID': str, 'Source_Key': str}, engine='openpyxl')
                    if 'Group_ID' not in selection_df.columns:
                        logger.error(f"-> 致命錯誤: 比較組設定檔 '{selection_path.name}' 中缺少 'Group_ID' 欄位。"); continue
                    items_to_process = selection_df['Group_ID'].dropna().unique()
                    logger.info(f"找到 {len(items_to_process)} 個比較組: {list(items_to_process)}")

                for i, item in enumerate(items_to_process):
                    item_str = str(item)
                    logger.info(f"--- 開始處理: {item_str} ---")
                    
                    # [關鍵修正] 建立描述性的工作表名稱
                    new_sheet_name = item_str
                    if processing_mode == 'selection_based' and not selection_df.empty:
                        current_group_df = selection_df[selection_df['Group_ID'] == item_str]
                        subject_row = current_group_df[current_group_df['DataType'] == '比準地']
                        if not subject_row.empty:
                            subject_id = str(subject_row.iloc[0]['ID'])
                            new_sheet_name = f"組{item_str}_比準地{subject_id}"
                        else:
                            logger.warning(f"   - 警告: 在 Group_ID '{item_str}' 中找不到 '比準地' 資料，將使用預設頁簽名稱 '{item_str}'。")
                    elif processing_mode == 'id_based':
                        new_sheet_name = f"宗地_{item_str}"
                    
                    new_sheet = wb.copy_worksheet(from_worksheet=template_sheet)
                    safe_sheet_name = re.sub(r'[\\/*?:\[\]]', '_', new_sheet_name)[:31]
                    new_sheet.title = safe_sheet_name
                    
                    context_kwargs: Dict[str, Any] = {}
                    if is_id_mode:
                        context_kwargs['target_id'] = item_str
                        mock_group_df = pd.DataFrame([{'Group_ID': f"ID_{item_str}", 'DataType': '比準地', 'Source_Key': main_src_key, 'ID': item_str}])
                        context_kwargs['group_df'] = mock_group_df
                    else:
                        context_kwargs['group_df'] = selection_df[selection_df['Group_ID'] == item_str]
                        context_kwargs['selection_path'] = selection_path
                    
                    rating_prefix = str(layout_config.get('rating_config_prefix', ''))
                    context_kwargs['mapping_path'] = template_path.with_name(f"{base_name}_mapping.xlsx")
                    context_kwargs['rulebook_path'] = template_path.with_name(f"{rating_prefix}_Rulebook.xlsx") if rating_prefix else None
                    context_kwargs['master_plan_path'] = template_path.with_name(f"{rating_prefix}_基準表.xlsx") if rating_prefix else None
                    context_kwargs['matrix_rulebook_path'] = template_path.with_name(f"{base_name}_MatrixRulebook.xlsx")
                    
                    context = HandlerContext(sheet=new_sheet, **base_context_params, **context_kwargs)
                    handler_instance.process(context)
                
                if "範本" in wb.sheetnames:
                    wb.remove(wb["範本"])
                wb.save(output_filename)
                logger.info(f"所有結果已整合並保存至: {output_filename}")

            else:
                logger.error(f"未知的處理模式: '{processing_mode}'")

        except Exception as e:
            logger.error(f"-> 處理任務 '{base_name}' 時發生嚴重錯誤: {e}")
            traceback.print_exc(file=open(output_dir / f"{base_name}_traceback.log", "w", encoding='utf-8'))
        
    print("\n=========================\n=== 所有任務執行完畢 ===\n=========================")

if __name__ == "__main__":
    main()