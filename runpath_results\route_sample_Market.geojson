{"type": "FeatureCollection", "bbox": [121.121953, 25.030264, 121.154576, 25.05522], "features": [{"bbox": [121.121953, 25.030264, 121.154576, 25.05522], "type": "Feature", "properties": {"segments": [{"distance": 5753.4, "duration": 493.2, "steps": [{"distance": 119.9, "duration": 28.8, "type": 11, "instruction": "Head northeast on 建國路", "name": "建國路", "way_points": [0, 2]}, {"distance": 43.4, "duration": 10.4, "type": 0, "instruction": "Turn left onto 民生路", "name": "民生路", "way_points": [2, 3]}, {"distance": 308.2, "duration": 37.0, "type": 1, "instruction": "Turn right onto 永吉街", "name": "永吉街", "way_points": [3, 8]}, {"distance": 219.5, "duration": 39.5, "type": 0, "instruction": "Turn left", "name": "-", "way_points": [8, 14]}, {"distance": 1303.8, "duration": 99.9, "type": 1, "instruction": "Turn right onto 大觀橋", "name": "大觀橋", "way_points": [14, 23]}, {"distance": 948.7, "duration": 68.3, "type": 1, "instruction": "Turn right onto 忠孝路, 桃35", "name": "忠孝路, 桃35", "way_points": [23, 43]}, {"distance": 213.4, "duration": 19.2, "type": 1, "instruction": "Turn right onto 大觀路二段, 15, 桃35", "name": "大觀路二段, 15, 桃35", "way_points": [43, 44]}, {"distance": 11.2, "duration": 4.0, "type": 2, "instruction": "Turn sharp left onto 桃35", "name": "桃35", "way_points": [44, 45]}, {"distance": 652.6, "duration": 47.0, "type": 1, "instruction": "Turn right onto 新生路, 桃35", "name": "新生路, 桃35", "way_points": [45, 52]}, {"distance": 1541.2, "duration": 111.0, "type": 2, "instruction": "Turn sharp left onto 福山路三段, 桃35-1", "name": "福山路三段, 桃35-1", "way_points": [52, 77]}, {"distance": 391.5, "duration": 28.2, "type": 0, "instruction": "Turn left onto 廣大路, 桃40", "name": "廣大路, 桃40", "way_points": [77, 82]}, {"distance": 0.0, "duration": 0.0, "type": 10, "instruction": "Arrive at 廣大路, 桃40, on the left", "name": "-", "way_points": [82, 82]}]}], "way_points": [0, 82], "summary": {"distance": 5753.4, "duration": 493.2}}, "geometry": {"coordinates": [[121.121953, 25.051458], [121.122282, 25.051701], [121.122887, 25.052126], [121.122634, 25.052442], [121.123056, 25.05271], [121.123366, 25.052799], [121.123643, 25.052849], [121.124383, 25.05292], [121.125574, 25.052955], [121.125535, 25.053332], [121.125507, 25.053738], [121.125511, 25.054081], [121.12547, 25.054329], [121.125467, 25.054487], [121.125434, 25.054923], [121.125788, 25.054933], [121.126957, 25.055009], [121.130248, 25.055206], [121.131279, 25.05522], [121.13154, 25.055208], [121.133854, 25.055105], [121.135506, 25.055035], [121.13666, 25.054993], [121.138359, 25.054905], [121.138503, 25.054444], [121.138663, 25.053928], [121.138989, 25.052898], [121.139145, 25.052658], [121.139598, 25.052184], [121.139966, 25.051845], [121.140117, 25.051687], [121.140789, 25.051026], [121.141013, 25.0508], [121.141271, 25.050547], [121.141561, 25.050253], [121.141893, 25.050045], [121.142012, 25.049986], [121.142333, 25.049888], [121.142524, 25.049789], [121.142676, 25.049663], [121.142854, 25.049485], [121.142986, 25.04937], [121.143268, 25.049115], [121.144002, 25.048414], [121.14243, 25.047128], [121.142541, 25.047135], [121.142762, 25.046128], [121.142996, 25.045212], [121.143238, 25.044125], [121.143253, 25.044061], [121.143563, 25.042817], [121.143727, 25.042065], [121.143888, 25.041394], [121.143964, 25.041472], [121.144129, 25.04153], [121.144211, 25.041515], [121.144832, 25.040722], [121.145116, 25.040436], [121.145299, 25.040363], [121.145663, 25.040266], [121.146291, 25.040147], [121.14662, 25.040022], [121.14687, 25.039895], [121.147403, 25.039257], [121.14749, 25.039175], [121.147923, 25.038694], [121.148309, 25.038035], [121.148995, 25.036684], [121.149499, 25.035727], [121.149627, 25.035489], [121.149666, 25.035396], [121.149678, 25.035278], [121.149689, 25.035162], [121.150088, 25.034072], [121.150217, 25.033725], [121.150388, 25.032946], [121.150668, 25.031774], [121.150999, 25.030264], [121.151465, 25.03043], [121.152995, 25.030999], [121.153563, 25.031229], [121.154538, 25.031623], [121.154576, 25.03164]], "type": "LineString"}}], "metadata": {"attribution": "openrouteservice.org | OpenStreetMap contributors", "service": "routing", "timestamp": *************, "query": {"coordinates": [[121.122044, 25.051357], [121.**************, 25.**************]], "profile": "driving-car", "profileName": "driving-car", "format": "g<PERSON><PERSON><PERSON>"}, "engine": {"version": "9.3.0", "build_date": "2025-06-06T15:39:25Z", "graph_date": "2025-06-23T11:47:36Z", "osm_date": "1970-01-01T00:00:00Z"}}, "crs": {"type": "name", "properties": {"name": "EPSG:4326"}}}