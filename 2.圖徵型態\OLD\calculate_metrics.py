import os
import math
from osgeo import ogr, gdal

def rotate_point(p, angle_rad, center_p=(0, 0)):
    """輔助函式：以一個中心點旋轉指定的點"""
    x, y = p[0] - center_p[0], p[1] - center_p[1]
    cos_a, sin_a = math.cos(angle_rad), math.sin(angle_rad)
    new_x = x * cos_a - y * sin_a + center_p[0]
    new_y = x * sin_a + y * cos_a + center_p[1]
    return (new_x, new_y)

def calculate_all_metrics_definitive(shapefile_path: str, fill_ratio_threshold: float, convexity_threshold: float):
    """
    最終完美版 (The Definitive Version):
    1. 【修正】Width 和 Depth 的定義，使其符合專業慣例。
    2. 採用您指定的最終欄位順序、名稱和精度。
    3. 包含清晰的分類準則註解。
    """
    gdal.UseExceptions()
    
    try:
        driver = ogr.GetDriverByName("ESRI Shapefile")
        dataSource = driver.Open(shapefile_path, 1)
        if dataSource is None: return
        layer = dataSource.GetLayer()
    except Exception as e:
        print(f"開啟 Shapefile 時發生錯誤: {e}"); return

    layer_defn = layer.GetLayerDefn()
    
    fields_to_create_ordered = [
        ("Centroid_X", {"type": ogr.OFTReal, "width": 12, "precision": 2}),
        ("Centroid_Y", {"type": ogr.OFTReal, "width": 12, "precision": 2}),
        ("OMBB_Long",  {"type": ogr.OFTReal, "width": 10, "precision": 1}),
        ("OMBB_Short", {"type": ogr.OFTReal, "width": 10, "precision": 1}),
        ("Width",      {"type": ogr.OFTReal, "width": 10, "precision": 1}),
        ("Depth",      {"type": ogr.OFTReal, "width": 10, "precision": 1}),
        ("FillRatio",  {"type": ogr.OFTReal, "width": 8, "precision": 1}),
        ("ShapeRatio", {"type": ogr.OFTReal, "width": 8, "precision": 1}),
        ("Convexity",  {"type": ogr.OFTReal, "width": 8, "precision": 1}),
        ("AssessType", {"type": ogr.OFTString, "width": 20}),
        ("Shape_Type", {"type": ogr.OFTString, "width": 20}),
    ]

    existing_fields = [layer_defn.GetFieldDefn(i).GetName() for i in range(layer_defn.GetFieldCount())]
    
    for name, props in fields_to_create_ordered:
        if name not in existing_fields:
            print(f"新增欄位: {name}")
            field_defn = ogr.FieldDefn(name, props['type'])
            if props['type'] == ogr.OFTReal:
                field_defn.SetWidth(props['width']); field_defn.SetPrecision(props['precision'])
            else:
                field_defn.SetWidth(props['width'])
            layer.CreateField(field_defn)

    print("\n開始全自動計算所有幾何指標及最終形態分類...")
    feature_count = layer.GetFeatureCount()
    layer.ResetReading()

    for i, feature in enumerate(layer):
        geom = feature.GetGeometryRef()
        fid = feature.GetFID()
        if geom is None or geom.IsEmpty() or geom.GetArea() < 1e-6: continue

        try:
            # --- 步驟 1: 計算所有基礎幾何指標 ---
            area_real = geom.GetArea()
            convex_hull = geom.ConvexHull()
            if convex_hull is None or convex_hull.IsEmpty(): continue
            
            area_convex_hull = convex_hull.GetArea()
            convexity_ratio = area_real / area_convex_hull if area_convex_hull > 1e-9 else 0.0
            
            ch_ring = convex_hull.GetGeometryRef(0)
            if ch_ring is None or ch_ring.GetPointCount() <= 2: continue
            ch_points = ch_ring.GetPoints()
            
            min_area, omBB_long, omBB_short, best_angle_rad = float('inf'), 0.0, 0.0, 0.0

            for j in range(len(ch_points) - 1):
                p1, p2 = ch_points[j], ch_points[j+1]
                angle = math.atan2(p2[1] - p1[1], p2[0] - p1[0])
                rotated_ch_points = [rotate_point(p, -angle) for p in ch_points]
                
                min_x, max_x = min(p[0] for p in rotated_ch_points), max(p[0] for p in rotated_ch_points)
                min_y, max_y = min(p[1] for p in rotated_ch_points), max(p[1] for p in rotated_ch_points)
                
                width_tmp, height_tmp = max_x - min_x, max_y - min_y
                area = width_tmp * height_tmp
                
                if area < min_area:
                    min_area, omBB_long, omBB_short = area, max(width_tmp, height_tmp), min(width_tmp, height_tmp)
                    best_angle_rad = angle if width_tmp > height_tmp else angle + math.pi / 2

            area_omBB = omBB_long * omBB_short
            fill_ratio = area_real / area_omBB if area_omBB > 1e-9 else 0.0
            
            # 【【【核心邏輯修正】】】
            # 將主方向 (best_angle_rad) 旋轉至與 Y 軸平行
            # 這樣旋轉後的高度就是「深度」，寬度就是「寬度」
            rotation_rad = -best_angle_rad

            original_boundary = geom.GetBoundary()
            if original_boundary is None: continue
            original_points = original_boundary.GetPoints()
            if not original_points: continue
            
            rotated_points = [rotate_point(p, rotation_rad) for p in original_points]
            
            r_min_x, r_max_x = min(p[0] for p in rotated_points), max(p[0] for p in rotated_points)
            r_min_y, r_max_y = min(p[1] for p in rotated_points), max(p[1] for p in rotated_points)
            
            # 根據新的、正確的定義賦值
            depth = r_max_y - r_min_y
            width = r_max_x - r_min_x
            
            longer_dim, shorter_dim = max(depth, width), min(depth, width)
            aspect_ratio = longer_dim / shorter_dim if shorter_dim > 1e-9 else float('inf')
            
            centroid = geom.Centroid()
            cx, cy = centroid.GetX(), centroid.GetY()

            # --- 步驟 2: 進行雙重分類 ---
            # 2a. 評估型態 (AssessType) - 客觀的幾何描述
            if convexity_ratio < 0.95:
                assessed_shape = "凹陷圖形"
            elif fill_ratio >= 0.85:
                assessed_shape = "矩形"
            else:
                assessed_shape = "凸邊非矩形"

            # 2b. 形狀分類 (Shape_Type) - 根據使用者定義的嚴格規則
            shape_type = "不規則形" # 先預設為不規則形
            if convexity_ratio >= convexity_threshold: # 第一關：必須是凸多邊形
                if fill_ratio >= fill_ratio_threshold:   # 第二關：必須是飽滿的
                    shape_type = "方形"
            
            # --- 步驟 3: 回填所有資料 (按新順序) ---
            feature.SetField("Centroid_X", cx)
            feature.SetField("Centroid_Y", cy)
            feature.SetField("OMBB_Long", omBB_long)
            feature.SetField("OMBB_Short", omBB_short)
            feature.SetField("Width", width)
            feature.SetField("Depth", depth)
            feature.SetField("FillRatio", fill_ratio)
            feature.SetField("ShapeRatio", aspect_ratio)
            feature.SetField("Convexity", convexity_ratio)
            feature.SetField("AssessType", assessed_shape)
            feature.SetField("Shape_Type", shape_type)
            
            layer.SetFeature(feature)
        except Exception as e:
            print(f"FID {fid}: 處理時發生未知錯誤: {e}")
            continue

        if (i + 1) % 100 == 0 or (i + 1) == feature_count:
            print(f"已處理 {i + 1} / {feature_count} 個圖徵...")

    dataSource = None
    print("\n處理完成！所有幾何指標及最終形態分類已成功回填。")


if __name__ == "__main__":
    shapefile_to_process = r"D:\GIS project\land expropriation\project\桃42線月桃路\GIS_data\徵收土地地籍謄本.shp"

    # =========================================================================
    # ==                      最終分類準則與閾值設定                         ==
    # =========================================================================
    CONVEXITY_THRESHOLD = 0.95
    FILL_RATIO_THRESHOLD = 0.75
    # =========================================================================

    if not os.path.exists(shapefile_to_process):
        print(f"錯誤：找不到檔案 '{shapefile_to_process}'")
    else:
        calculate_all_metrics_definitive(
            shapefile_to_process,
            fill_ratio_threshold=FILL_RATIO_THRESHOLD,
            convexity_threshold=CONVEXITY_THRESHOLD
        )