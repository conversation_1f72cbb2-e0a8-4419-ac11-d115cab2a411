# check_paths.py - 系統路徑與檔案診斷工具
from pathlib import Path
import json

print("--- 系統路徑與檔案診斷工具 ---")
try:
    # 1. 檢查目前工作目錄
    BASE_DIR = Path.cwd()
    print(f"1. 程式執行根目錄 (BASE_DIR): {BASE_DIR}\n")

    # 2. 檢查 project_config.json 路徑與內容
    config_path = BASE_DIR / "form_input" / "config" / "project_config.json"
    print(f"2. 預期設定檔路徑: {config_path}")
    if not config_path.exists():
        print("   -> [錯誤] 在此路徑找不到 project_config.json！請確認檔案位置。")
    else:
        print("   -> [成功] 找到 project_config.json。")
        with open(config_path, 'r', encoding='utf-8') as f:
            project_config = json.load(f)
            paths = project_config.get("paths", {})
            form_input_path = paths.get("form_input", "form_input/")
            print(f"3. 從設定檔讀取到的 form_input 路徑: '{form_input_path}'\n")

            # 4. 構造並檢查最終的 templates 資料夾路徑
            template_dir = BASE_DIR / form_input_path / "templates"
            print(f"4. 最終構造出的範本資料夾路徑: {template_dir}")
            if not template_dir.exists():
                print("   -> [致命錯誤] 這個範本資料夾不存在！請檢查第 3 步的路徑設定是否正確。")
            else:
                print("   -> [成功] 這個範本資料夾存在。\n")

                # 5. 掃描資料夾內符合條件的檔案
                print("5. 正在掃描資料夾內所有 `*_template.xlsx` 檔案...")
                tasks_found = list(template_dir.glob("*_template.xlsx"))
                
                if not tasks_found:
                    print("\n   -> [診斷結果] 找不到任何符合 `*_template.xlsx` 模式的檔案。")
                    print("      請務必檢查：")
                    print("      - 您的範本檔名是否【確實】以 `_template.xlsx` 結尾？")
                    print("      - 副檔名是否為【小寫】的 `.xlsx`？")
                    
                    print("\n--- 以下是資料夾內找到的所有檔案列表 ---")
                    all_files = list(template_dir.iterdir())
                    if not all_files:
                        print("    (資料夾是空的)")
                    else:
                        for f in all_files:
                            print(f" - {f.name}")
                    print("------------------------------------------")
                else:
                    print("\n   -> [成功] 找到了以下可處理的任務：")
                    for task_path in tasks_found:
                        print(f"      - {task_path.name}")
except Exception as e:
    print(f"\n[執行期間發生錯誤]: {e}")