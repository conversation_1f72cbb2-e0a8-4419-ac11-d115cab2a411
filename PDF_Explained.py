# ==============================================================================
# 程式名稱: PDF_Explained.py
# 版本: 3.3.0 (Final)
# 最後修訂日期: 2025-07-28
#
# 重要修訂事項:
# - v3.3.0:
#   - [調整] 將 '公私有別' 和 '宗地流水號_私' 欄位移至表格末尾。
#   - [調整] 將 '宗地流水號公私有別' 正式更名為 '宗地流水號_私'。
#   - [修正] create_excel_report 函式現在直接接收 DataFrame，避免資料遺失。
# - v3.2.0:
#   - 新增公私有別與對應流水號功能。
# ==============================================================================

import pdfplumber
import re
import pandas as pd
import os
from pathlib import Path
import json
from typing import Dict, List, Any, Tuple
import logging
from datetime import datetime
import pytesseract
from PIL import Image
import openpyxl
from openpyxl.styles import Font, Alignment
from openpyxl.utils import get_column_letter
from itertools import groupby
from operator import itemgetter
import math

def setup_logging(output_dir: Path) -> logging.Logger:
    log_dir = output_dir / "logs"
    log_dir.mkdir(exist_ok=True)
    logger = logging.getLogger("PDF_Explained_Logger")
    logger.setLevel(logging.INFO)
    if logger.hasHandlers():
        logger.handlers.clear()
    c_handler = logging.StreamHandler()
    c_format = logging.Formatter('%(levelname)s - %(message)s')
    c_handler.setFormatter(c_format)
    logger.addHandler(c_handler)
    log_file = log_dir / f"process_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    f_handler = logging.FileHandler(log_file, encoding='utf-8')
    f_format = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    f_handler.setFormatter(f_format)
    logger.addHandler(f_handler)
    return logger

class TranscriptParser:
    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        self.logger = logger
        self.config = config
        self.FULL_TO_HALF = str.maketrans("０１２３４５６７８９ＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚ：－（）／", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz:-()/")
        self.LABEL_CANNOT_EXTRACT = "無法擷取"
        self.LABEL_IS_BLANK = "(空白)"
        self.LABEL_NOT_APPLICABLE = "此項無資料"
        self.ORDERED_LAND_FIELDS = ["登記日期", "登記原因", "地    目", "等    則", "面    積", "使用分區", "使用地類別", "公告土地現值", "地上建物建號", "其他登記事項"]
        self.ORDERED_OWNER_FIELDS = ["登記日期", "登記原因", "原因發生日期", "所有權人", "統一編號", "出生日期", "住    址", "權利範圍", "權狀字號", "當期申報地價", "前次移轉現值或原規定地價", "歷次取得權利範圍", "其他登記事項"]
        self.FINAL_OWNER_KEYS = ["登記次序", "登記日期", "登記原因", "原因發生日期", "所有權人", "統一編號", "出生日期", "住址", "管理者", "管理者統一編號", "管理者住址", "權利範圍", "權狀字號", "當期申報地價", "前次移轉現值或原規定地價", "歷次取得權利範圍", "相關他項權利登記次序", "其他登記事項"]
        self.CSV_COLUMN_ORDER = ["宗地流水號", "地段", "地號", "資料查詢時間", "標示部_登記日期", "標示部_登記原因", "標示部_地目", "標示部_等則", "標示部_面積", "標示部_使用分區", "標示部_使用地類別", "標示部_公告土地現值", "標示部_地上建物建號", "標示部_其他登記事項", "所有權部_登記次序", "所有權部_登記日期", "所有權部_登記原因", "所有權部_原因發生日期", "所有權部_所有權人", "所有權部_統一編號", "所有權部_出生日期", "所有權部_住址", "所有權部_管理者", "所有權部_管理者統一編號", "所有權部_管理者住址", "所有權部_權利範圍", "所有權部_權狀字號", "所有權部_當期申報地價", "所有權部_前次移轉現值或原規定地價", "所有權部_歷次取得權利範圍", "所有權部_相關他項權利登記次序", "所有權部_其他登記事項", "資料顯示完畢", "來源檔案"]
        if self.config.get("tesseract_cmd_path"):
            pytesseract.pytesseract.tesseract_cmd = self.config["tesseract_cmd_path"]

    def _extract_field(self, block_text: str, field_label: str, stop_keywords: List[str]) -> str:
        label_pattern = r'\s*'.join(field_label.split())
        stop_patterns = [r'\s*'.join(s.split()) for s in stop_keywords]
        stop_pattern = "|".join(stop_patterns) if stop_keywords else '(?!)'
        pattern = re.compile(rf"{label_pattern}\s*[:：]?\s*(.*?)(?=\s*(?:{stop_pattern}|$))", re.DOTALL)
        match = pattern.search(block_text)
        if match:
            raw_captured_group = match.group(1)
            cleaned_text = re.sub(r'[\*\(\)=;_\?!]', '', raw_captured_group)
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
            value = cleaned_text.translate(self.FULL_TO_HALF)
            return value if value else self.LABEL_IS_BLANK
        return self.LABEL_CANNOT_EXTRACT

    def _clean_trailing_junk(self, text: str) -> str:
        if not isinstance(text, str): return text
        patterns_to_strip = [r'\s*(?:資料顯示完畢|頁次[:：]?\s*\d+)\s*$', r'\s*\(續次頁\).*$', r'\s+(?:[A-Z]\d*|\d+[A-Z]?)(?:\s+[A-Z]\d*|\s+\d+[A-Z]?)*$', r'\s+\d+(?:\s+\d+)*$', r'\s+(?:土地|地號|號)$']
        for pattern in patterns_to_strip:
            text = re.sub(pattern, '', text, flags=re.DOTALL).strip()
        if re.fullmatch(r'\(空白\)(?:\s+[^<>]+)*\s*(?:< *>)?', text):
            text = self.LABEL_IS_BLANK
        return text

    def _format_price_field(self, price_str: str) -> str:
        price_str = self._clean_trailing_junk(price_str)
        if not isinstance(price_str, str) or price_str in [self.LABEL_CANNOT_EXTRACT, self.LABEL_IS_BLANK]: return price_str
        price_str = re.sub(r'(\d)\s+(\d)', r'\1\2', price_str)
        pattern = re.compile(r'(?:民國)?(\d+年\d+月)\s*([\d,.]+元/平方公尺)')
        match = pattern.search(price_str)
        if match: return f"{match.group(1).replace(' ', '')} {match.group(2)}"
        fallback_match = re.search(r'([\d,.]+元/平方公尺)', price_str)
        if fallback_match: return fallback_match.group(1).strip()
        return self.LABEL_CANNOT_EXTRACT

    def parse_pdf(self, pdf_path: Path) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        full_text_pages = []
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text_plumber = page.extract_text(x_tolerance=2, y_tolerance=2, layout=True)
                    processed_page_text = ""
                    if not page_text_plumber or len(page_text_plumber.strip()) < 50:
                        self.logger.info(f"  [OCR] 檔案 '{pdf_path.name}' 頁面 {page.page_number} 文字不足，嘗試 OCR...")
                        try:
                            page_image = page.to_image(resolution=300).original
                            processed_page_text = pytesseract.image_to_string(page_image, lang='chi_tra', config='--psm 6')
                            if not processed_page_text.strip():
                                self.logger.warning(f"  [OCR] 頁面 {page.page_number} OCR 辨識結果為空，回退 pdfplumber 結果。")
                                processed_page_text = page_text_plumber
                            else:
                                processed_page_text = re.sub(r'\s+', ' ', processed_page_text).strip()
                                processed_page_text = processed_page_text.translate(self.FULL_TO_HALF)
                        except Exception as ocr_e:
                            self.logger.error(f"  [OCR錯誤] 頁面 {page.page_number} 執行 OCR 失敗: {ocr_e}，回退 pdfplumber 結果。")
                            processed_page_text = page_text_plumber
                    else:
                        processed_page_text = page_text_plumber
                    full_text_pages.append(processed_page_text)
            full_text = "\n".join(full_text_pages)
        except Exception as e:
            self.logger.error(f"  [錯誤] 讀取 PDF 檔案 '{pdf_path.name}' 時發生錯誤: {e}")
            return [], []
        all_results_from_pdf, failed_blocks = [], []
        transcript_chunks = re.finditer(r"土\s*地\s*建\s*物\s*查\s*詢\s*資\s*料.*?〈\s*資料顯示完畢\s*〉", full_text, re.DOTALL)
        for match in transcript_chunks:
            chunk = match.group(0)
            header_match = re.search(r"(?P<district>\S+段)\s+(?P<land_number>[\d-]+)地號\s*資料查詢時間：(?P<query_time>.*?)\s*頁次：", chunk, re.DOTALL)
            if not header_match: continue
            header_data = header_match.groupdict()
            data_complete_marker = "是" if re.search(r'〈\s*資料顯示完畢\s*〉', chunk) else "否"
            land_info = {}
            land_description_match = re.search(r"土地標示部(.*?(?=所有權部|他項權利部|$))", chunk, re.DOTALL)
            if land_description_match:
                land_description_text = land_description_match.group(1)
                for i, field in enumerate(self.ORDERED_LAND_FIELDS):
                    stop_keywords = [f for f in self.ORDERED_LAND_FIELDS if f != field]
                    land_info[field.replace(" ", "")] = self._extract_field(land_description_text, field, stop_keywords)
                category_val, price_val = land_info.get("使用地類別", ""), land_info.get("公告土地現值", "")
                date_pattern = r"(民國\s*\d+\s*年\s*\d+\s*月)"
                if (date_match := re.search(date_pattern, category_val)) and not re.search(date_pattern, price_val):
                    date_str = date_match.group(1)
                    cleaned_category = category_val.replace(date_str, "").strip()
                    land_info["使用地類別"] = cleaned_category if cleaned_category else self.LABEL_IS_BLANK
                    land_info["公告土地現值"] = f"{date_str.replace(' ', '')} {price_val}"
                land_info["公告土地現值"] = self._format_price_field(land_info["公告土地現值"])
                value = land_info.get("其他登記事項", "")
                value = self._clean_trailing_junk(value)
                re測_match = re.search(r'(重測前[:：]?\s*[\u4e00-\u9fa5A-Za-z\s.-]+(?:[0-9A-Z]+(?:-[0-9A-Z]+)*)?(?:地號|號))', value)
                if re測_match: value = re測_match.group(0).strip()
                general_note_match = re.search(r'(一般註記事項)(依據桃園市政府.*?函核准(?:使用分區第一次劃定及使用地編定)?)', value)
                if general_note_match: value = f"{general_note_match.group(1)}{general_note_match.group(2).replace(' ', '').replace(':', '：')}"
                land_info["其他登記事項"] = value if value else self.LABEL_IS_BLANK
            ownership_parts = []
            ownership_sections = re.finditer(r"所有權部(.*?(?=(?:所有權部|他項權利部|$)))", chunk, re.DOTALL)
            for section_match in ownership_sections:
                section_text = section_match.group(1)
                for block in re.split(r'(?=\s*（\d+）登記次序)', section_text)[1:]:
                    if not block.strip(): continue
                    owner_info = {}
                    reg_order_match = re.search(r"（\d+）登記次序\s*[:：]\s*(\S+)", block)
                    owner_info["登記次序"] = reg_order_match.group(1) if reg_order_match else self.LABEL_CANNOT_EXTRACT
                    for i, field in enumerate(self.ORDERED_OWNER_FIELDS):
                        stop_keywords = [f for f in self.ORDERED_OWNER_FIELDS if f != field]
                        owner_info[field.replace(" ","")] = self._extract_field(block, field, stop_keywords)
                    if "登記原因" in owner_info: owner_info["登記原因"] = self._clean_trailing_junk(owner_info["登記原因"]).split(' ')[0].strip()
                    cause_date = self._clean_trailing_junk(owner_info.get("原因發生日期", self.LABEL_CANNOT_EXTRACT))
                    if cause_date not in [self.LABEL_CANNOT_EXTRACT, self.LABEL_IS_BLANK]:
                        date_match = re.search(r'(民國\d+年\d+月\d+日)', cause_date)
                        owner_info["原因發生日期"] = date_match.group(1) if date_match else self.LABEL_CANNOT_EXTRACT
                    owner_name = self._clean_trailing_junk(owner_info.get("所有權人", self.LABEL_CANNOT_EXTRACT))
                    if owner_name not in [self.LABEL_CANNOT_EXTRACT, self.LABEL_IS_BLANK]:
                        name_match = re.search(r'^([\u4e00-\u9fa5A-Za-z\s.-]+)', owner_name)
                        owner_info["所有權人"] = name_match.group(1).strip() if name_match else self.LABEL_CANNOT_EXTRACT
                    uni_id = self._clean_trailing_junk(owner_info.get("統一編號", self.LABEL_CANNOT_EXTRACT))
                    if uni_id not in [self.LABEL_CANNOT_EXTRACT, self.LABEL_IS_BLANK]:
                        id_match = re.search(r'^([A-Z]?[0-9A-Z]{7,10})$', uni_id)
                        owner_info["統一編號"] = id_match.group(1).strip() if id_match else self.LABEL_CANNOT_EXTRACT
                    birth_date = self._clean_trailing_junk(owner_info.get("出生日期", self.LABEL_CANNOT_EXTRACT))
                    if birth_date not in [self.LABEL_CANNOT_EXTRACT, self.LABEL_IS_BLANK]:
                        date_match = re.search(r'(民國\d+年\d+月\d+日)', birth_date)
                        owner_info["出生日期"] = date_match.group(1) if date_match else self.LABEL_CANNOT_EXTRACT
                    if "權利範圍" in owner_info:
                        owner_info["權利範圍"] = self._clean_trailing_junk(owner_info["權利範圍"])
                        if (share_match := re.search(r'^\s*(\S*分之\S*|全部)$', owner_info["權利範圍"])): owner_info["權利範圍"] = share_match.group(1)
                    if "權狀字號" in owner_info:
                        owner_info["權狀字號"] = self._clean_trailing_junk(owner_info["權狀字號"]).replace("---(空白)", "空白").strip()
                        if owner_info["權狀字號"] == "空白字第號": owner_info["權狀字號"] = self.LABEL_IS_BLANK
                    owner_info["當期申報地價"] = self._format_price_field(owner_info.get("當期申報地價"))
                    owner_info["前次移轉現值或原規定地價"] = self._format_price_field(owner_info.get("前次移轉現值或原規定地價"))
                    value = self._clean_trailing_junk(owner_info.get("歷次取得權利範圍", self.LABEL_CANNOT_EXTRACT))
                    core_share_match = re.search(r'^(全部\s*\d*分之\d*|\d+\s*分之\d+)', value)
                    value = core_share_match.group(0).strip() if core_share_match else self.LABEL_IS_BLANK
                    if (other_rights_match := re.search(r'(.*?)(?:相關他項權利登記次序\s*[:：]\s*(\S+))', value, re.DOTALL)):
                        owner_info['歷次取得權利範圍'], owner_info['相關他項權利登記次序'] = other_rights_match.group(1).strip(), other_rights_match.group(2).strip()
                    else:
                        owner_info['歷次取得權利範圍'], owner_info['相關他項權利登記次序'] = value, self.LABEL_IS_BLANK
                    value = self._clean_trailing_junk(owner_info.get("其他登記事項", self.LABEL_CANNOT_EXTRACT))
                    value = re.sub(r'(\d)\s+(\d)', r'\1\2', value)
                    value = re.sub(r'(辦理公有土地權利登記)[\s\S]*', r'\1', value)
                    owner_info["其他登記事項"] = value if value != '' else self.LABEL_IS_BLANK
                    raw_addr_text = owner_info.get("住址", "")
                    manager_regex = re.compile(r'(?P<pre_addr>.*?)(?:管\s*理\s*者\s*[:：]?\s*(?P<name>[\u4e00-\u9fa5A-Za-z\s.-]+)(?:\s*統一編號\s*[:：]?\s*(?P<id>[\dA-Z]+(?:-[\dA-Z]+)*))?(?:\s*住\s*址\s*[:：]?\s*(?P<addr>.*))?)', re.DOTALL)
                    manager_match = manager_regex.search(raw_addr_text)
                    if manager_match and manager_match.group('name') is not None:
                        owner_info['管理者'] = manager_match.group('name').strip() or self.LABEL_IS_BLANK
                        owner_info['管理者統一編號'] = manager_match.group('id').strip() if manager_match.group('id') else self.LABEL_IS_BLANK
                        owner_info['管理者住址'] = manager_match.group('addr').strip() if manager_match.group('addr') else self.LABEL_IS_BLANK
                        cleaned_owner_address = manager_match.group('pre_addr').strip()
                        owner_info['住址'] = cleaned_owner_address if cleaned_owner_address else self.LABEL_IS_BLANK
                    else:
                        owner_info['管理者'], owner_info['管理者統一編號'], owner_info['管理者住址'] = self.LABEL_NOT_APPLICABLE, self.LABEL_NOT_APPLICABLE, self.LABEL_NOT_APPLICABLE
                        owner_info['住址'] = raw_addr_text
                    key_fields_to_check = ['所有權人', '統一編號', '住址']
                    is_failure = any(owner_info.get(field) == self.LABEL_CANNOT_EXTRACT for field in key_fields_to_check)
                    if is_failure:
                        failure_reason = ", ".join([f for f in key_fields_to_check if owner_info.get(f) == self.LABEL_CANNOT_EXTRACT])
                        self.logger.warning(f"  [解析失敗] 檔案: {pdf_path.name}, 地號: {header_data.get('land_number')}, 登記次序: {owner_info['登記次序']}. 原因: {failure_reason} 無法擷取。")
                        failed_blocks.append({"source_file": pdf_path.name, "district": header_data.get("district"), "land_number": header_data.get("land_number"), "registration_order": owner_info['登記次序'], "raw_text_block": block.strip()})
                    ownership_parts.append({key: owner_info.get(key, self.LABEL_NOT_APPLICABLE) for key in self.FINAL_OWNER_KEYS})
            all_results_from_pdf.append({"地段": header_data.get("district", self.LABEL_CANNOT_EXTRACT), "地號": header_data.get("land_number", self.LABEL_CANNOT_EXTRACT), "資料查詢時間": " ".join(header_data.get("query_time", self.LABEL_CANNOT_EXTRACT).split()), "土地標示部": land_info, "所有權部": ownership_parts, "資料顯示完畢": data_complete_marker, "來源檔案": pdf_path.name})
        return all_results_from_pdf, failed_blocks

def create_final_table(data_list: list[dict[str, any]], csv_column_order: list[str]) -> pd.DataFrame:
    records = []
    for data in data_list:
        if not data: continue
        common_info = {k: data.get(k) for k in ["宗地流水號", "地段", "地號", "資料查詢時間", "資料顯示完畢", "來源檔案"]}
        land_info = data.get("土地標示部", {})
        if not isinstance(land_info, dict): land_info = {}
        common_info.update({f"標示部_{k}": v for k, v in land_info.items()})
        owner_parts = data.get("所有權部")
        if not owner_parts:
            records.append(common_info)
        else:
            for owner_part in owner_parts:
                record = common_info.copy()
                if isinstance(owner_part, dict):
                    record.update({f"所有權部_{k}": v for k, v in owner_part.items()})
                records.append(record)
    if not records: return pd.DataFrame()
    return pd.DataFrame(records).reindex(columns=csv_column_order)

def _split_district(full_district_str: str) -> tuple[str, str]:
    if not isinstance(full_district_str, str): return "未知行政區", "未知地段"
    match = re.match(r'(.*?(?:市|區|鎮|鄉))(.*)', full_district_str)
    if match: return match.group(1).strip(), match.group(2).strip()
    return "未知行政區", full_district_str.strip()

def _format_share(share_str: str) -> str:
    if not isinstance(share_str, str): return "格式錯誤"
    if "全部" in str(share_str): return "1/1"
    fraction_match = re.search(r'(\d+)\s*分之\s*(\d+)', str(share_str))
    if fraction_match: return f"{fraction_match.group(2)}/{fraction_match.group(1)}"
    return str(share_str).strip()

def _write_printable_sheet(ws, district_records, land_summary_map, project_name):
    ws.delete_rows(1, ws.max_row + 1)
    ws.delete_cols(1, ws.max_column + 1)
    ROWS_PER_CHUNK, COLS_PER_DATA_BLOCK, BLOCKS_PER_ROW = 25, 3, 4
    PAGE_ROW_GAP, HEADER_ROWS, MAX_ROWS_PER_PAGE = 2, 5, 34
    font_bold_large = Font(name='標楷體', bold=True, size=16)
    font_bold_normal = Font(name='標楷體', bold=True, size=12)
    font_normal = Font(name='標楷體', size=11)
    align_center = Alignment(horizontal='center', vertical='center')
    align_left = Alignment(horizontal='left', vertical='center')
    for i in range(BLOCKS_PER_ROW):
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 1)].width = 5
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 2)].width = 16
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 3)].width = 12
    plots_data = {k: list(v) for k, v in groupby(district_records, key=itemgetter('宗地流水號'))}
    plot_keys = sorted(plots_data.keys(), key=lambda x: str(x))
    current_page_start_row, current_col_offset = 1, 0
    row_heights_in_current_page_row = [0]
    def write_page_headers(start_row):
        ws.merge_cells(start_row=start_row, start_column=1, end_row=start_row, end_column=12)
        cell = ws.cell(start_row, 1, "共有土地共有人及對應持分附表"); cell.font = font_bold_large; cell.alignment = align_center
        ws.merge_cells(start_row=start_row+1, start_column=1, end_row=start_row+1, end_column=12)
        cell = ws.cell(start_row+1, 1, project_name); cell.font = font_normal; cell.alignment = align_left
        return start_row + 3
    current_row = write_page_headers(current_page_start_row)
    for serial_no in plot_keys:
        plot_records = plots_data[serial_no]
        if not plot_records: continue
        num_chunks = math.ceil(len(plot_records) / ROWS_PER_CHUNK)
        rows_needed_for_block = HEADER_ROWS + ROWS_PER_CHUNK
        if current_col_offset > 0 and (current_col_offset + COLS_PER_DATA_BLOCK * num_chunks > COLS_PER_DATA_BLOCK * BLOCKS_PER_ROW):
             current_row += max(row_heights_in_current_page_row) + PAGE_ROW_GAP
             current_col_offset, row_heights_in_current_page_row = 0, [0]
        if current_row - current_page_start_row + rows_needed_for_block > MAX_ROWS_PER_PAGE:
            current_page_start_row = ws.max_row + PAGE_ROW_GAP + 1 if ws.max_row > 1 else MAX_ROWS_PER_PAGE
            current_row = write_page_headers(current_page_start_row)
            current_col_offset, row_heights_in_current_page_row = 0, [0]
        start_col = 1 + current_col_offset
        record_sample = plot_records[0]
        for i, label in enumerate(["宗地流水號", "鄉鎮市區", "段小段名稱", "地號"]):
            ws.cell(current_row + i, start_col, label).font = font_bold_normal
            ws.merge_cells(start_row=current_row + i, start_column=start_col + 1, end_row=current_row + i, end_column=start_col + COLS_PER_DATA_BLOCK * num_chunks - 1)
            ws.cell(current_row + i, start_col + 1, record_sample.get(label, "")).font = font_normal if i > 0 else font_bold_normal
        data_start_row = current_row + HEADER_ROWS
        for i, record in enumerate(plot_records):
            chunk_idx, row_in_chunk = divmod(i, ROWS_PER_CHUNK)
            col_offset_in_block = chunk_idx * COLS_PER_DATA_BLOCK
            if row_in_chunk == 0:
                summary_text = land_summary_map.get((record['地段'], record['地號']), '')
                for j, sub_header in enumerate(["土地所有權人或管理人", summary_text, "持分"]):
                    ws.cell(data_start_row - 1, start_col + col_offset_in_block + j, sub_header).font = font_bold_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block, i + 1).font = font_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block + 1, record['土地所有權人或管理人']).font = font_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block + 2, record['持分']).font = font_normal
        current_col_offset += COLS_PER_DATA_BLOCK * num_chunks
        row_heights_in_current_page_row.append(rows_needed_for_block)

def create_excel_report(df_source: pd.DataFrame, output_path: Path, project_name: str):
    if df_source.empty:
        pd.DataFrame(columns=df_source.columns).to_excel(output_path, sheet_name='各地段土地謄本', index=False)
        return
    all_records = []
    for _, row in df_source.iterrows():
        manager = row.get('所有權部_管理者')
        owner = row.get('所有權部_所有權人')
        person_in_charge = manager if pd.notna(manager) and manager not in ["", "此項無資料", "(空白)"] else owner
        all_records.append({'地段': row.get('地段'), '地號': row.get('地號'), '登記次序': row.get('所有權部_登記次序'), '宗地流水號': row.get('宗地流水號'), '土地所有權人或管理人': person_in_charge, '持分_原始': row.get('所有權部_權利範圍')})
    
    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        df_source_with_summary = df_source.copy()
        district_records_list = sorted(all_records, key=lambda r: (str(r.get('地段','')), str(r.get('地號','')), int(str(r.get('登記次序', '0')).split('(')[0]) if str(r.get('登記次序', '0')).split('(')[0].isdigit() else 9999))
        land_summary_map = {}
        for land_id, group in groupby(district_records_list, key=lambda r: (r['地段'], r['地號'])):
            group_list = list(group)
            if not group_list: continue 
            count = len(group_list)
            first_owner_name = group_list[0].get('土地所有權人或管理人', "無法擷取") 
            land_summary_map[land_id] = f"{first_owner_name} 等{count}人" if count > 1 else first_owner_name
        
        df_source_with_summary['總共有人數'] = df_source_with_summary.apply(lambda row: land_summary_map.get((row['地段'], row['地號']), "此項無資料"), axis=1)
        df_source_with_summary.to_excel(writer, sheet_name='各地段土地謄本', index=False)
        
        for record in district_records_list:
            record['鄉鎮市區'], record['段小段名稱'] = _split_district(record.get('地段', ''))
            record['持分'] = _format_share(record.get('持分_原始', ''))
        appendix_rows, headers = [], ["宗地流水號", "鄉鎮市區", "段小段名稱", "地號", "登記次序", "土地所有權人或管理人", "持分", "有權人與持分彙總"]
        processed_summaries = set()
        for record in district_records_list:
            land_id = (record['地段'], record['地號'])
            summary = land_summary_map.get(land_id, "") if land_id not in processed_summaries else ""
            if summary: processed_summaries.add(land_id)
            appendix_rows.append([record.get('宗地流水號'), record.get('鄉鎮市區'), record.get('段小段名稱'), record.get('地號'), record.get('登記次序'), record.get('土地所有權人或管理人'), record.get('持分'), summary])
        df_appendix = pd.DataFrame(appendix_rows, columns=headers)
        df_appendix.to_excel(writer, sheet_name='各地段對應持分附表', index=False)
        
        wb = writer.book
        ws_printable = wb.create_sheet("各地段對應持分附表(可列印)")
        _write_printable_sheet(ws_printable, district_records_list, land_summary_map, project_name)

def add_custom_columns(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty: return df
    def classify_ownership(uni_id: any) -> str:
        if not isinstance(uni_id, str) or pd.isna(uni_id): return "私有地"
        uni_id = str(uni_id).strip()
        if uni_id == "0000000158": return "公有地"
        else: return "私有地"
    df['公私有別'] = df['所有權部_統一編號'].apply(classify_ownership)
    private_land_df = df[df['公私有別'] == '私有地'].copy()
    if not private_land_df.empty:
        private_unique_plots = private_land_df[['地段', '地號']].drop_duplicates().sort_values(by=['地段', '地號'])
        private_serial_map = {tuple(row): f"{i+1:04d}" for i, row in enumerate(private_unique_plots.itertuples(index=False))}
        plot_tuples = pd.Series(list(zip(df['地段'], df['地號'])))
        df['宗地流水號_私'] = plot_tuples.map(private_serial_map)
    if '宗地流水號_私' not in df.columns: df['宗地流水號_私'] = None
    df['宗地流水號_私'] = df['宗地流水號_私'].fillna('')
    return df

def main():
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("錯誤：找不到 config.json 檔案。請建立設定檔。")
        return

    project_dir = Path(__file__).resolve().parent
    input_dir = project_dir / config['input_dir']
    output_dir = project_dir / config['output_dir']
    input_dir.mkdir(exist_ok=True)
    output_dir.mkdir(exist_ok=True)
    logger = setup_logging(output_dir)

    pdf_files = list(input_dir.glob("*.pdf"))
    if not pdf_files:
        logger.error(f"錯誤：在資料夾 '{input_dir}' 中找不到 PDF。")
        return

    logger.info("="*50)
    logger.info(f"專案名稱: {config['project_name']}")
    logger.info(f"找到 {len(pdf_files)} 個 PDF 檔案，開始處理...")
    logger.info("="*50)
    
    parser = TranscriptParser(config, logger)
    all_parsed_data, all_failed_blocks, successful_files = [], [], 0

    for pdf_path in pdf_files:
        logger.info(f"\n--- 正在處理: {pdf_path.name} ---")
        try:
            parsed_data, failed_blocks = parser.parse_pdf(pdf_path)
            if not parsed_data and not failed_blocks:
                logger.warning(f"  [注意] 未能從 '{pdf_path.name}' 中辨識出任何謄本區塊。")
                continue
            if parsed_data: all_parsed_data.extend(parsed_data)
            if failed_blocks: all_failed_blocks.extend(failed_blocks)
            successful_files += 1
        except Exception as e:
            logger.critical(f"  [嚴重錯誤] 處理 '{pdf_path.name}' 時發生未預期的錯誤: {e}", exc_info=True)

    if not all_parsed_data:
        logger.warning("\n處理完成，但未能解析出任何資料。")
    else:
        unique_plots = sorted(list(set((d.get('地段'), d.get('地號')) for d in all_parsed_data)))
        serial_map = {plot_key: f"{i+1:04d}" for i, plot_key in enumerate(unique_plots)}
        for item in all_parsed_data:
            item['宗地流水號'] = serial_map.get((item.get('地段'), item.get('地號')))
        
        final_df_all = create_final_table(all_parsed_data, parser.CSV_COLUMN_ORDER)
        final_df_all = add_custom_columns(final_df_all)
        
        final_column_order = parser.CSV_COLUMN_ORDER + ["公私有別", "宗地流水號_私"]
        final_df_all = final_df_all.reindex(columns=final_column_order)

        for district, df_group in final_df_all.groupby('地段'):
            if pd.isna(district): continue
            base_name = str(district).replace(" ", "")
            district_data_list = [item for item in all_parsed_data if item.get('地段') == district]
            df_group.to_csv(output_dir / f"{base_name}.csv", index=False, encoding='utf-8-sig')
            with open(output_dir / f"{base_name}.json", 'w', encoding='utf-8') as f:
                json.dump(district_data_list, f, ensure_ascii=False, indent=4)
            create_excel_report(df_group, output_dir / f"土地清冊及持分附表--{base_name}.xlsx", config['project_name'])
            logger.info(f"  -> 已為地段 '{district}' 產生獨立報告。")

        if successful_files > 0:
            logger.info("\n--- 正在產生合併報告 ---")
            final_df_all.to_csv(output_dir / "consolidated_land_data.csv", index=False, encoding='utf-8-sig')
            logger.info(f"✅ 合併的 CSV 檔案已儲存。")
            with open(output_dir / "consolidated_land_data.json", 'w', encoding='utf-8') as f:
                json.dump(all_parsed_data, f, ensure_ascii=False, indent=4)
            logger.info(f"📝 合併的 JSON 診斷檔案已儲存。")
            create_excel_report(final_df_all, output_dir / "consolidated_land_data.xlsx", config['project_name'])
            logger.info(f"✅ 合併的 Excel 報告已儲存。")

    if all_failed_blocks:
        log_dir = output_dir / "logs"
        failure_output_path = log_dir / "failed_cases_for_llm.json"
        logger.info("-" * 50)
        logger.warning(f"偵測到 {len(all_failed_blocks)} 個解析失敗的區塊。")
        logger.warning(f"詳細原始文本已歸檔至: {failure_output_path}")
        with open(failure_output_path, 'w', encoding='utf-8') as f:
            json.dump(all_failed_blocks, f, ensure_ascii=False, indent=4)
    
    logger.info("\n" + "="*50)
    logger.info("✅ 處理完成！")
    logger.info(f"總共掃描 {len(pdf_files)} 個 PDF，成功處理 {successful_files} 個。")
    if all_parsed_data:
        final_table_for_count = create_final_table(all_parsed_data, parser.CSV_COLUMN_ORDER)
        logger.info(f"總共解析 {len(all_parsed_data)} 個地號區塊，產生 {len(final_table_for_count)} 筆所有權記錄。")
    logger.info("="*50)

if __name__ == "__main__":
    main()