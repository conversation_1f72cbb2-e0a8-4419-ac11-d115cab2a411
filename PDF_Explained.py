# ==============================================================================
# 程式名稱: PDF_Explained.py
# 版本: 3.4.0 (Final)
# 最後修訂日期: 2025-07-29
#
# 重要修訂事項:
# - v3.4.0:
#   - [新增] 在 Excel 報告中，額外生成一份 "各地段對應持分附表(私有地)" 工作表。
#   - [修正] 統一所有報告生成邏輯至 generate_excel_report 函式中。
# - v3.3.0:
#   - 調整 '公私有別' 和 '宗地流水號_私' 欄位至表格末尾並更名。
# ==============================================================================

import pdfplumber
import re
import pandas as pd
import os
from pathlib import Path
import json
from typing import Dict, List, Any, Tuple
import logging
from datetime import datetime
import pytesseract
from PIL import Image
import openpyxl
from openpyxl.styles import Font, Alignment
from openpyxl.utils import get_column_letter
from itertools import groupby
from operator import itemgetter
import math

# --- 輔助函式與類別定義 ---
def setup_logging(output_dir: Path) -> logging.Logger:
    log_dir = output_dir / "logs"
    log_dir.mkdir(exist_ok=True)
    logger = logging.getLogger("PDF_Explained_Logger")
    logger.setLevel(logging.INFO)
    if logger.hasHandlers():
        logger.handlers.clear()
    c_handler = logging.StreamHandler()
    c_format = logging.Formatter('%(levelname)s - %(message)s')
    c_handler.setFormatter(c_format)
    logger.addHandler(c_handler)
    log_file = log_dir / f"process_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    f_handler = logging.FileHandler(log_file, encoding='utf-8')
    f_format = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    f_handler.setFormatter(f_format)
    logger.addHandler(f_handler)
    return logger

class TranscriptParser:
    # (此類別的程式碼與 v3.3.0 完全相同，為求簡潔省略)
    pass

def create_final_table(data_list: list[dict[str, any]], csv_column_order: list[str]) -> pd.DataFrame:
    # (此函式的程式碼與 v3.3.0 完全相同，為求簡潔省略)
    pass

def _split_district(full_district_str: str) -> tuple[str, str]:
    if not isinstance(full_district_str, str): return "未知行政區", "未知地段"
    match = re.match(r'(.*?(?:市|區|鎮|鄉))(.*)', full_district_str)
    if match: return match.group(1).strip(), match.group(2).strip()
    return "未知行政區", full_district_str.strip()

def _format_share(share_str: str) -> str:
    if not isinstance(share_str, str): return "格式錯誤"
    if "全部" in str(share_str): return "1/1"
    fraction_match = re.search(r'(\d+)\s*分之\s*(\d+)', str(share_str))
    if fraction_match: return f"{fraction_match.group(2)}/{fraction_match.group(1)}"
    return str(share_str).strip()

def _write_printable_sheet(ws, district_records, land_summary_map, project_name):
    ws.delete_rows(1, ws.max_row + 1)
    ws.delete_cols(1, ws.max_column + 1)
    ROWS_PER_CHUNK, COLS_PER_DATA_BLOCK, BLOCKS_PER_ROW = 25, 3, 4
    PAGE_ROW_GAP, HEADER_ROWS, MAX_ROWS_PER_PAGE = 2, 5, 34
    font_bold_large = Font(name='標楷體', bold=True, size=16)
    font_bold_normal = Font(name='標楷體', bold=True, size=12)
    font_normal = Font(name='標楷體', size=11)
    align_center = Alignment(horizontal='center', vertical='center')
    align_left = Alignment(horizontal='left', vertical='center')
    for i in range(BLOCKS_PER_ROW):
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 1)].width = 5
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 2)].width = 16
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 3)].width = 12
    plots_data = {k: list(v) for k, v in groupby(district_records, key=itemgetter('宗地流水號'))}
    plot_keys = sorted(plots_data.keys(), key=lambda x: str(x))
    current_page_start_row, current_col_offset = 1, 0
    row_heights_in_current_page_row = [0]
    def write_page_headers(start_row):
        ws.merge_cells(start_row=start_row, start_column=1, end_row=start_row, end_column=12)
        cell = ws.cell(start_row, 1, "共有土地共有人及對應持分附表"); cell.font = font_bold_large; cell.alignment = align_center
        ws.merge_cells(start_row=start_row+1, start_column=1, end_row=start_row+1, end_column=12)
        cell = ws.cell(start_row+1, 1, project_name); cell.font = font_normal; cell.alignment = align_left
        return start_row + 3
    current_row = write_page_headers(current_page_start_row)
    for serial_no in plot_keys:
        plot_records = plots_data[serial_no]
        if not plot_records: continue
        num_chunks = math.ceil(len(plot_records) / ROWS_PER_CHUNK)
        rows_needed_for_block = HEADER_ROWS + ROWS_PER_CHUNK
        if current_col_offset > 0 and (current_col_offset + COLS_PER_DATA_BLOCK * num_chunks > COLS_PER_DATA_BLOCK * BLOCKS_PER_ROW):
             current_row += max(row_heights_in_current_page_row) + PAGE_ROW_GAP
             current_col_offset, row_heights_in_current_page_row = 0, [0]
        if current_row - current_page_start_row + rows_needed_for_block > MAX_ROWS_PER_PAGE:
            current_page_start_row = ws.max_row + PAGE_ROW_GAP + 1 if ws.max_row > 1 else MAX_ROWS_PER_PAGE
            current_row = write_page_headers(current_page_start_row)
            current_col_offset, row_heights_in_current_page_row = 0, [0]
        start_col = 1 + current_col_offset
        record_sample = plot_records[0]
        for i, label in enumerate(["宗地流水號", "鄉鎮市區", "段小段名稱", "地號"]):
            ws.cell(current_row + i, start_col, label).font = font_bold_normal
            ws.merge_cells(start_row=current_row + i, start_column=start_col + 1, end_row=current_row + i, end_column=start_col + COLS_PER_DATA_BLOCK * num_chunks - 1)
            ws.cell(current_row + i, start_col + 1, record_sample.get(label, "")).font = font_normal if i > 0 else font_bold_normal
        data_start_row = current_row + HEADER_ROWS
        for i, record in enumerate(plot_records):
            chunk_idx, row_in_chunk = divmod(i, ROWS_PER_CHUNK)
            col_offset_in_block = chunk_idx * COLS_PER_DATA_BLOCK
            if row_in_chunk == 0:
                summary_text = land_summary_map.get((record['地段'], record['地號']), '')
                for j, sub_header in enumerate(["土地所有權人或管理人", summary_text, "持分"]):
                    ws.cell(data_start_row - 1, start_col + col_offset_in_block + j, sub_header).font = font_bold_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block, i + 1).font = font_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block + 1, record['土地所有權人或管理人']).font = font_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block + 2, record['持分']).font = font_normal
        current_col_offset += COLS_PER_DATA_BLOCK * num_chunks
        row_heights_in_current_page_row.append(rows_needed_for_block)

def generate_excel_report(df_source: pd.DataFrame, output_path: Path, project_name: str):
    if df_source.empty:
        pd.DataFrame(columns=df_source.columns).to_excel(output_path, sheet_name='各地段土地謄本', index=False)
        return

    def prepare_printable_sheet_data(df: pd.DataFrame):
        all_records = []
        for _, row in df.iterrows():
            manager = row.get('所有權部_管理者')
            owner = row.get('所有權部_所有權人')
            person_in_charge = manager if pd.notna(manager) and manager not in ["", "此項無資料", "(空白)"] else owner
            all_records.append({'地段': row.get('地段'), '地號': row.get('地號'), '登記次序': row.get('所有權部_登記次序'), '宗地流水號': row.get('宗地流水號'), '土地所有權人或管理人': person_in_charge, '持分_原始': row.get('所有權部_權利範圍')})
        
        district_records_list = sorted(all_records, key=lambda r: (str(r.get('地段','')), str(r.get('地號','')), int(str(r.get('登記次序', '0')).split('(')[0]) if str(r.get('登記次序', '0')).split('(')[0].isdigit() else 9999))
        land_summary_map = {}
        for land_id, group in groupby(district_records_list, key=lambda r: (r['地段'], r['地號'])):
            group_list = list(group)
            if not group_list: continue 
            count = len(group_list)
            first_owner_name = group_list[0].get('土地所有權人或管理人', "無法擷取") 
            land_summary_map[land_id] = f"{first_owner_name} 等{count}人" if count > 1 else first_owner_name
        
        for record in district_records_list:
            record['鄉鎮市區'], record['段小段名稱'] = _split_district(record.get('地段', ''))
            record['持分'] = _format_share(record.get('持分_原始', ''))
            
        return district_records_list, land_summary_map

    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        # --- 主工作表 ---
        df_source_with_summary = df_source.copy()
        _, land_summary_map_all = prepare_printable_sheet_data(df_source_with_summary)
        df_source_with_summary['總共有人數'] = df_source_with_summary.apply(lambda row: land_summary_map_all.get((row['地段'], row['地號']), "此項無資料"), axis=1)
        df_source_with_summary.to_excel(writer, sheet_name='各地段土地謄本', index=False)

        # --- 標準附表 (全部) ---
        district_records_list_all, _ = prepare_printable_sheet_data(df_source)
        appendix_rows, headers = [], ["宗地流水號", "鄉鎮市區", "段小段名稱", "地號", "登記次序", "土地所有權人或管理人", "持分", "有權人與持分彙總"]
        processed_summaries = set()
        for record in district_records_list_all:
            land_id = (record['地段'], record['地號'])
            summary = land_summary_map_all.get(land_id, "") if land_id not in processed_summaries else ""
            if summary: processed_summaries.add(land_id)
            appendix_rows.append([record.get('宗地流水號'), record.get('鄉鎮市區'), record.get('段小段名稱'), record.get('地號'), record.get('登記次序'), record.get('土地所有權人或管理人'), record.get('持分'), summary])
        df_appendix = pd.DataFrame(appendix_rows, columns=headers)
        df_appendix.to_excel(writer, sheet_name='各地段對應持分附表', index=False)
        
        wb = writer.book
        # --- 可列印附表 (全部) ---
        ws_printable_all = wb.create_sheet("各地段對應持分附表(可列印)")
        _write_printable_sheet(ws_printable_all, district_records_list_all, land_summary_map_all, project_name)

        # --- [v3.4.0 新增] 可列印附表 (僅私有地) ---
        df_private = df_source[df_source['公私有別'] == '私有地'].copy()
        if not df_private.empty:
            district_records_list_private, land_summary_map_private = prepare_printable_sheet_data(df_private)
            ws_printable_private = wb.create_sheet("各地段對應持分附表(私有地)")
            _write_printable_sheet(ws_printable_private, district_records_list_private, land_summary_map_private, project_name)

def add_custom_columns(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty: return df
    def classify_ownership(uni_id: any) -> str:
        if not isinstance(uni_id, str) or pd.isna(uni_id): return "私有地"
        uni_id = str(uni_id).strip()
        if uni_id == "0000000158": return "公有地"
        else: return "私有地"
    df['公私有別'] = df['所有權部_統一編號'].apply(classify_ownership)
    private_land_df = df[df['公私有別'] == '私有地'].copy()
    if not private_land_df.empty:
        private_unique_plots = private_land_df[['地段', '地號']].drop_duplicates().sort_values(by=['地段', '地號'])
        private_serial_map = {tuple(row): f"{i+1:04d}" for i, row in enumerate(private_unique_plots.itertuples(index=False))}
        plot_tuples = pd.Series(list(zip(df['地段'], df['地號'])))
        df['宗地流水號_私'] = plot_tuples.map(private_serial_map)
    if '宗地流水號_私' not in df.columns: df['宗地流水號_私'] = None
    df['宗地流水號_私'] = df['宗地流水號_私'].fillna('')
    return df

def main():
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print("錯誤：找不到 config.json 檔案。請建立設定檔。")
        return

    project_dir = Path(__file__).resolve().parent
    input_dir = project_dir / config['input_dir']
    output_dir = project_dir / config['output_dir']
    input_dir.mkdir(exist_ok=True)
    output_dir.mkdir(exist_ok=True)
    logger = setup_logging(output_dir)

    pdf_files = list(input_dir.glob("*.pdf"))
    if not pdf_files:
        logger.error(f"錯誤：在資料夾 '{input_dir}' 中找不到 PDF。")
        return

    logger.info("="*50)
    logger.info(f"專案名稱: {config.get('project_name', '未命名專案')}")
    logger.info(f"找到 {len(pdf_files)} 個 PDF 檔案，開始處理...")
    logger.info("="*50)
    
    parser = TranscriptParser(config, logger)
    all_parsed_data, all_failed_blocks, successful_files = [], [], 0

    for pdf_path in pdf_files:
        logger.info(f"\n--- 正在處理: {pdf_path.name} ---")
        try:
            parsed_data, failed_blocks = parser.parse_pdf(pdf_path)
            if not parsed_data and not failed_blocks:
                logger.warning(f"  [注意] 未能從 '{pdf_path.name}' 中辨識出任何謄本區塊。")
                continue
            if parsed_data: all_parsed_data.extend(parsed_data)
            if failed_blocks: all_failed_blocks.extend(failed_blocks)
            successful_files += 1
        except Exception as e:
            logger.critical(f"  [嚴重錯誤] 處理 '{pdf_path.name}' 時發生未預期的錯誤: {e}", exc_info=True)

    if not all_parsed_data:
        logger.warning("\n處理完成，但未能解析出任何資料。")
    else:
        unique_plots = sorted(list(set((d.get('地段'), d.get('地號')) for d in all_parsed_data)))
        serial_map = {plot_key: f"{i+1:04d}" for i, plot_key in enumerate(unique_plots)}
        for item in all_parsed_data:
            item['宗地流水號'] = serial_map.get((item.get('地段'), item.get('地號')))
        
        final_df_all = create_final_table(all_parsed_data, parser.CSV_COLUMN_ORDER)
        final_df_all = add_custom_columns(final_df_all)
        
        final_column_order = parser.CSV_COLUMN_ORDER + ["公私有別", "宗地流水號_私"]
        final_df_all = final_df_all.reindex(columns=final_column_order)
        
        project_name = config.get('project_name', '未命名專案')

        for district, df_group in final_df_all.groupby('地段'):
            if pd.isna(district): continue
            base_name = str(district).replace(" ", "")
            district_data_list = [item for item in all_parsed_data if item.get('地段') == district]
            df_group.to_csv(output_dir / f"{base_name}.csv", index=False, encoding='utf-8-sig')
            with open(output_dir / f"{base_name}.json", 'w', encoding='utf-8') as f:
                json.dump(district_data_list, f, ensure_ascii=False, indent=4)
            generate_excel_report(df_group, output_dir / f"土地清冊及持分附表--{base_name}.xlsx", project_name)
            logger.info(f"  -> 已為地段 '{district}' 產生獨立報告。")

        if successful_files > 0:
            logger.info("\n--- 正在產生合併報告 ---")
            final_df_all.to_csv(output_dir / "consolidated_land_data.csv", index=False, encoding='utf-8-sig')
            logger.info(f"✅ 合併的 CSV 檔案已儲存。")
            with open(output_dir / "consolidated_land_data.json", 'w', encoding='utf-8') as f:
                json.dump(all_parsed_data, f, ensure_ascii=False, indent=4)
            logger.info(f"📝 合併的 JSON 診斷檔案已儲存。")
            generate_excel_report(final_df_all, output_dir / "consolidated_land_data.xlsx", project_name)
            logger.info(f"✅ 合併的 Excel 報告已儲存。")

    if all_failed_blocks:
        log_dir = output_dir / "logs"
        failure_output_path = log_dir / "failed_cases_for_llm.json"
        logger.info("-" * 50)
        logger.warning(f"偵測到 {len(all_failed_blocks)} 個解析失敗的區塊。")
        logger.warning(f"詳細原始文本已歸檔至: {failure_output_path}")
        with open(failure_output_path, 'w', encoding='utf-8') as f:
            json.dump(all_failed_blocks, f, ensure_ascii=False, indent=4)
    
    logger.info("\n" + "="*50)
    logger.info("✅ 處理完成！")
    logger.info(f"總共掃描 {len(pdf_files)} 個 PDF，成功處理 {successful_files} 個。")
    if all_parsed_data:
        final_table_for_count = create_final_table(all_parsed_data, parser.CSV_COLUMN_ORDER)
        logger.info(f"總共解析 {len(all_parsed_data)} 個地號區塊，產生 {len(final_table_for_count)} 筆所有權記錄。")
    logger.info("="*50)

if __name__ == "__main__":
    main()