# ===================================================================
#  檔案名稱: handlers/comparison_matrix_handler.py
#  版    本: v6.8 - Syntax Fix
#  說    明: 移除了因複製錯誤導致的語法錯誤。
# ===================================================================
import pandas as pd
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.utils.cell import coordinate_from_string
from openpyxl.utils import column_index_from_string
from typing import Dict, Any, Optional, List
from pathlib import Path
import logging

from .base import BaseHandler, HandlerContext
from . import utils

class ComparisonMatrixHandler(BaseHandler):

    def _get_single_item_rating(self, target_id: str, source_key: str, item_name: str, use_type: str, context: HandlerContext) -> Optional[str]:
        rating_mapping = self.rating_mapping
        rating_rulebook = self.rating_rulebook
        gdfs = context.gdfs
        gis_sources_config = context.gis_sources_config

        item_map_rule = rating_mapping[rating_mapping['ItemName'] == item_name]
        if item_map_rule.empty: return None
        item_map_rule = item_map_rule.iloc[0]
        rule_type = item_map_rule.get('RuleType')
        raw_input_value = None
        gis_field = item_map_rule.get('Source_GIS_Field')
        
        if not source_key or source_key not in gdfs: return None

        id_column = gis_sources_config.get(source_key, {}).get('id_column')
        if pd.notna(gis_field) and id_column:
            gdf = gdfs[source_key]
            target_rows = gdf[gdf[id_column].astype(str) == str(target_id)]
            if not target_rows.empty: raw_input_value = target_rows.iloc[0].get(gis_field)
        
        if pd.isna(raw_input_value): return None
        
        rule_alias = item_map_rule.get('RuleAlias_ItemName')
        rule_lookup_name = rule_alias if pd.notna(rule_alias) else item_name
        active_rules = rating_rulebook[(rating_rulebook['UseType'].astype(str).str.contains(use_type, na=False)) & (rating_rulebook['ItemName'] == rule_lookup_name)]
        if active_rules.empty: return None

        rating_text = None
        if rule_type == 'CHOICE':
            match = active_rules[active_rules['OptionText'].astype(str).str.strip() == str(raw_input_value).strip()]
            if not match.empty: rating_text = match.iloc[0].get('ScoreText')
        elif rule_type == 'NUMBER_RANGE':
            input_val = utils.clean_and_convert_to_float(raw_input_value)
            if input_val is not None:
                sorted_rules = active_rules.sort_values(by='Param1', ascending=True)
                for _, rule_row in sorted_rules.iterrows():
                    param1 = rule_row.get('Param1')
                    if pd.notna(param1) and input_val <= float(param1):
                        rating_text = rule_row.get('ScoreText'); break
                if rating_text is None:
                    default_rule = sorted_rules[sorted_rules['Param1'].isna()]
                    if not default_rule.empty: rating_text = default_rule.iloc[0].get('ScoreText')
        return rating_text

    def _compile_matrix_rules(self, matrix_rulebook_sheets: Dict[str, pd.DataFrame], logger: logging.Logger) -> pd.DataFrame:
        logger.info("   - [編譯] 開始編譯交叉矩陣規則...")
        all_rules = []
        for sheet_name, df in matrix_rulebook_sheets.items():
            if sheet_name.lower() == 'compiled_rules': continue
            use_type = sheet_name; current_item_name = None; header = []
            for index, row in df.iterrows():
                first_cell_value = str(row.iloc[0]) if pd.notna(row.iloc[0]) else ""
                if first_cell_value.startswith('ItemName:'): current_item_name = first_cell_value.replace('ItemName:', '').strip(); continue
                if first_cell_value.startswith('Rule:'): current_item_name = None; continue
                if first_cell_value.startswith('v 比準地'): header = [str(h).strip() for h in row.iloc[1:] if pd.notna(h)]; continue
                if current_item_name and header:
                    subject_rating = first_cell_value.strip()
                    if not subject_rating: continue
                    for i, comp_rating in enumerate(header):
                        correction_val = row.iloc[i + 1]
                        if pd.notna(correction_val): all_rules.append({'UseType': use_type, 'ItemName': current_item_name, 'Subject_Rating': subject_rating, 'Comparable_Rating': comp_rating, 'Correction_Percentage': correction_val})
        if not all_rules: 
            logger.warning("   - [編譯] 警告：未從 _MatrixRulebook.xlsx 編譯出任何規則。")
            return pd.DataFrame()
        compiled_df = pd.DataFrame(all_rules)
        logger.info(f"   - [編譯] 成功！共編譯 {len(compiled_df)} 條交叉比較規則。")
        return compiled_df

    def _lookup_correction(self, use_type: str, item_name: str, subject_rating: Optional[str], comp_rating: Optional[str]) -> Optional[float]:
        compiled_rules = self.compiled_matrix_rules
        if not all([use_type, item_name, subject_rating, comp_rating]): return None
        try:
            # 確保進行比較時，所有評級都是字串型別
            result = compiled_rules[
                (compiled_rules['UseType'] == use_type) & 
                (compiled_rules['ItemName'] == item_name) & 
                (compiled_rules['Subject_Rating'] == str(subject_rating)) & 
                (compiled_rules['Comparable_Rating'] == str(comp_rating))
            ]
            if not result.empty: 
                return float(result['Correction_Percentage'].iloc[0])
        except Exception: 
            pass # 靜默處理查詢失敗，返回 None
        return None

    def _parse_anchor_mapping(self, df: pd.DataFrame, logger: logging.Logger) -> Dict[str, Any]:
        anchors = {}
        known_keys = ["Subject_ID_Cell", "Subject_Rating_StartCell", "Comparable_Start_ID_Cell", "Comparable_Start_Rating_StartCell", "Comparable_Start_Correction_StartCell", "Comparable_Block_Width", "Subject_ItemName_StartCell", "Title_Cell", "Title_Numbering_Map"]
        for _, row in df.iterrows():
            row_items = [str(item).strip() for item in row if pd.notna(item)]
            if not row_items: continue
            found_key = None
            for key in known_keys:
                if key in row_items:
                    found_key = key; break
            if found_key and found_key not in anchors:
                for item in row_items:
                    if item == found_key: continue
                    if found_key == "Title_Numbering_Map" and ":" in item and "," in item:
                        anchors[found_key] = item; break
                    elif len(item) >= 2 and item[0].isalpha() and item[1:].isdigit():
                        anchors[found_key] = item; break
                    elif found_key == "Comparable_Block_Width" and item.isdigit():
                        anchors[found_key] = item; break
        logger.info(f"   - 成功解析佈局錨點: {anchors}")
        return anchors

    def process(self, context: HandlerContext) -> bool:
        logger = context.logger
        logger.info(f"--- 使用策略: 'ComparisonMatrixHandler' v6.8 (Syntax Fix) ---")
        
        try:
            rating_prefix = str(context.layout_config.get('rating_config_prefix', ''))
            rating_mapping_path = context.template_path.with_name(f"{rating_prefix}_mapping.xlsx") if rating_prefix else context.mapping_path
            rating_rulebook_path = context.template_path.with_name(f"{rating_prefix}_Rulebook.xlsx") if rating_prefix else None
            master_plan_path = context.template_path.with_name(f"{rating_prefix}_基準表.xlsx") if rating_prefix else None
            matrix_rulebook_path = context.template_path.with_name(f"{context.base_name}_MatrixRulebook.xlsx")
            
            if not rating_mapping_path.exists(): logger.error(f"找不到評級 mapping 檔案: {rating_mapping_path}"); return False
            self.rating_mapping = pd.read_excel(rating_mapping_path, sheet_name='RuleBasedLogic', engine='openpyxl')
            
            self.rating_rulebook = pd.read_excel(rating_rulebook_path, sheet_name='Rules', engine='openpyxl') if rating_rulebook_path and rating_rulebook_path.exists() else pd.DataFrame()
            if self.rating_rulebook.empty: logger.warning(f"未找到或內容為空的評級規則檔案 (Rulebook): {rating_rulebook_path}，評分功能將受限。")

            self.master_plan_df = pd.read_excel(master_plan_path) if master_plan_path and master_plan_path.exists() else pd.DataFrame()
            if self.master_plan_df.empty: logger.warning(f"未找到或內容為空的基準表 (MasterPlan): {master_plan_path}，評估項目列表可能不完整。")

            if context.mapping_path is None or not context.mapping_path.exists():
                logger.error(f"找不到佈局錨點 mapping 檔案: {context.mapping_path}"); return False
            anchor_df = pd.read_excel(context.mapping_path, header=None, engine='openpyxl')
            self.anchors = self._parse_anchor_mapping(anchor_df, logger)
            if not self.anchors:
                logger.error(f"-> 致命錯誤: 未能從 '{context.mapping_path.name}' 解析出任何有效的佈局錨點。"); return False

            matrix_rulebook_sheets = pd.read_excel(matrix_rulebook_path, sheet_name=None, engine='openpyxl') if matrix_rulebook_path.exists() else {}
            self.compiled_matrix_rules = self._compile_matrix_rules(matrix_rulebook_sheets, logger)

        except Exception as e:
            logger.error(f"-> 致命錯誤: 讀取設定檔時發生未知錯誤: {e}"); return False

        group_df = context.group_df
        if group_df is None or group_df.empty:
            logger.error("此處理模式需要有效的 group_df，但未提供。"); return False
        
        current_group_id = group_df['Group_ID'].iloc[0]

        subject_row = group_df[group_df['DataType'] == '比準地']
        if subject_row.empty:
            logger.warning(f"   - 警告: Group_ID '{current_group_id}' 中找不到'比準地'。"); return True
        
        subject_id = str(subject_row.iloc[0]['ID'])
        subject_source_key = str(subject_row.iloc[0]['Source_Key'])
        
        use_type_field = str(context.layout_config.get('use_type_field_in_shp', ''))
        main_source_key = str(context.layout_config.get('main_data_source_key', subject_source_key))
        id_column_main = str(context.gis_sources_config.get(main_source_key, {}).get('id_column'))
        main_gdf = context.gdfs.get(main_source_key, pd.DataFrame())
        subject_main_data_rows = main_gdf[main_gdf[id_column_main].astype(str) == str(subject_id)]
        
        if subject_main_data_rows.empty:
            logger.error(f"   - 數據查找錯誤: 在 '{main_source_key}' 中找不到 ID '{subject_id}'。"); return False
        subject_main_data = subject_main_data_rows.iloc[0]
        current_use_type = str(subject_main_data.get(use_type_field) or "").strip()
        if not current_use_type:
            logger.error(f"   - 數據內容錯誤: ID '{subject_id}' 的 UseType 欄位 '{use_type_field}' 為空。"); return False
        logger.info(f"   - 成功確定評估上下文 -> Group: {current_group_id}, UseType: '{current_use_type}'")

        if 'Title_Cell' in self.anchors:
            try:
                title_cell_coord = self.anchors['Title_Cell']
                original_title = context.sheet[title_cell_coord].value or ""
                base_title = original_title.split('（')[0]
                numbering_map_str = str(self.anchors.get('Title_Numbering_Map', '')).replace('"', '').replace("'", "")
                numbering_map = dict(item.split(':') for item in numbering_map_str.split(',') if ':' in item)
                title_number = numbering_map.get(current_use_type, 'X-X')
                new_title = f"表{title_number} {base_title}（{current_use_type}）"
                context.sheet[title_cell_coord].value = new_title
                logger.info(f"   - 動態更新報告標題為: '{new_title}'")
            except Exception as e: logger.warning(f"   - 更新標題時發生錯誤: {e}")

        item_name_list = self.master_plan_df[self.master_plan_df['UseType'] == current_use_type]['ItemName'].dropna().tolist() if not self.master_plan_df.empty else []
        if item_name_list: logger.info(f"   - 根據基準表，'{current_use_type}' 需要評估 {len(item_name_list)} 個項目。")
        else: logger.warning(f"   - 注意：基準表中未找到 '{current_use_type}' 的適用項目，將無法進行評級。")

        logger.info("   --- [階段一] 自動評定優劣等級 ---")
        all_ratings: Dict[str, Dict[str, Optional[str]]] = {}
        all_parcels = pd.concat([subject_row, group_df[group_df['DataType'] == '比較標的']])
        
        for _, parcel_row in all_parcels.iterrows():
            if pd.isna(parcel_row.get('ID')) or pd.isna(parcel_row.get('Source_Key')):
                logger.warning(f"警告: 在 Group_ID '{current_group_id}' 中發現無效的資料列 (ID或Source_Key為空)，已跳過。")
                continue
            parcel_id = str(parcel_row['ID'])
            parcel_source_key = str(parcel_row['Source_Key'])

            all_ratings[parcel_id] = {}
            logger.info(f"     - 正在評級 ID: {parcel_id} (來源: {parcel_source_key})")
            for item_name in item_name_list:
                rating = self._get_single_item_rating(parcel_id, parcel_source_key, item_name, current_use_type, context)
                all_ratings[parcel_id][item_name] = rating
                if rating: logger.info(f"       - {item_name}: {rating}")

        logger.info("   --- [階段二&三] 開始交叉查詢並填寫報告 ---")
        subj_id_col_str, subj_id_row = coordinate_from_string(self.anchors['Subject_ID_Cell'])
        utils.write_to_cell(context.sheet, subj_id_row, column_index_from_string(subj_id_col_str), subject_id, logger)
        
        rating_col_str, rating_row_start = coordinate_from_string(self.anchors['Subject_Rating_StartCell'])
        rating_col_idx = column_index_from_string(rating_col_str)
        item_name_col_str, item_name_row_start = coordinate_from_string(self.anchors['Subject_ItemName_StartCell'])
        item_name_col_idx = column_index_from_string(item_name_col_str)

        for i in range(len(item_name_list) + 20):
            current_row = item_name_row_start + i
            if current_row > context.sheet.max_row: break
            template_item_name_val = context.sheet.cell(row=current_row, column=item_name_col_idx).value
            if template_item_name_val:
                template_item_name = str(template_item_name_val).strip()
                if template_item_name in all_ratings.get(subject_id, {}):
                    rating = all_ratings[subject_id][template_item_name]
                    utils.write_to_cell(context.sheet, rating_row_start + i, rating_col_idx, rating, logger)

        comparables = group_df[group_df['DataType'] == '比較標的'].reset_index(drop=True)
        for i, comp_row in comparables.iterrows():
            if pd.isna(comp_row.get('ID')): continue
            comp_id = str(comp_row['ID'])

            logger.info(f"     - 正在處理比較標的 {i+1} (ID: {comp_id})")
            block_width = int(self.anchors.get('Comparable_Block_Width', 3))
            col_offset = i * block_width
            
            start_id_col_str, start_id_row = coordinate_from_string(self.anchors['Comparable_Start_ID_Cell'])
            current_id_col = column_index_from_string(start_id_col_str) + col_offset
            utils.write_to_cell(context.sheet, start_id_row, current_id_col, comp_id, logger)
            
            start_rating_col_str, start_rating_row = coordinate_from_string(self.anchors['Comparable_Start_Rating_StartCell'])
            current_rating_col = column_index_from_string(start_rating_col_str) + col_offset
            
            start_corr_col_str, start_corr_row = coordinate_from_string(self.anchors['Comparable_Start_Correction_StartCell'])
            current_corr_col = column_index_from_string(start_corr_col_str) + col_offset
            
            for j in range(len(item_name_list) + 20):
                current_row = item_name_row_start + j
                if current_row > context.sheet.max_row: break
                template_item_name_val = context.sheet.cell(row=current_row, column=item_name_col_idx).value
                if not template_item_name_val: continue
                template_item_name = str(template_item_name_val).strip()
                if not (template_item_name in item_name_list): continue
                
                subject_rating = all_ratings.get(subject_id, {}).get(template_item_name)
                comp_rating = all_ratings.get(comp_id, {}).get(template_item_name)
                
                utils.write_to_cell(context.sheet, start_rating_row + j, current_rating_col, comp_rating, logger)
                
                correction = self._lookup_correction(current_use_type, template_item_name, subject_rating, comp_rating)
                utils.write_to_cell(context.sheet, start_corr_row + j, current_corr_col, correction, logger)
        
        return True