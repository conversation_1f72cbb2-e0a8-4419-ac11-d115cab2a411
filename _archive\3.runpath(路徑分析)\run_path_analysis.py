import os
import re
import json
import sys
from dotenv import load_dotenv
from osgeo import ogr, gdal
import requests
import pandas as pd
import numpy as np
from pyproj import Transformer
import traceback

# ==============================================================================
# 【設定區】 - V12 終極典藏版
# ==============================================================================
load_dotenv()
ORS_API_KEY = os.getenv('ORS_API_KEY')

INPUT_FOLDER = 'runpath_input'
OUTPUT_FOLDER = 'runpath_results'
START_POINTS_FILE = os.path.join(INPUT_FOLDER, 'start_points.json')
SHP_FILE_PATH = r"D:\GIS project\land expropriation\project\桃42線月桃路\GIS_data\徵收土地地籍謄本.shp"
CONSOLIDATED_REPORT_XLSX = os.path.join(OUTPUT_FOLDER, 'consolidated_analysis_report.xlsx')
CONSOLIDATED_REPORT_JSON = os.path.join(OUTPUT_FOLDER, 'consolidated_analysis_report.json')
SKIP_KEYWORD = "無"
GENERATE_GEOJSON_ROUTES = True
CENTROID_X_FIELD = 'Centroid_X'
CENTROID_Y_FIELD = 'Centroid_Y'
LAND_ID_FIELD = 'landno14'
SRC_EPSG = 'EPSG:3826'
DEST_EPSG = 'EPSG:4326'

# ==============================================================================
# 輔助函數
# ==============================================================================
def setup_environment():
    for folder in [INPUT_FOLDER, OUTPUT_FOLDER]:
        if not os.path.exists(folder): os.makedirs(folder); print(f"已創建資料夾: {folder}")
    try: gdal.UseExceptions(); ogr.UseExceptions()
    except AttributeError: print("警告：您的 GDAL 版本較舊，無法設定 UseExceptions()。")

def transform_coords(points, src_epsg, dest_epsg):
    transformer = Transformer.from_crs(src_epsg, dest_epsg, always_xy=True)
    return [transformer.transform(p[0], p[1]) for p in points]

def get_route_geojson(start_coord_wgs84, end_coord_wgs84, api_key):
    headers = {'Authorization': api_key, 'Content-Type': 'application/json'}
    body = {"coordinates": [start_coord_wgs84, end_coord_wgs84]}
    try:
        response = requests.post('https://api.openrouteservice.org/v2/directions/driving-car/geojson', json=body, headers=headers, timeout=30)
        response.raise_for_status()
        route_data = response.json()
        route_data['crs'] = {"type": "name", "properties": {"name": "EPSG:4326"}}
        return route_data
    except requests.exceptions.RequestException as e:
        print(f"      警告：無法獲取路線 GeoJSON。原因: {e}"); return None

# ==============================================================================
# 主程式邏輯 (V12)
# ==============================================================================
def main():
    try:
        setup_environment()
        print("--- 開始執行【V12 - 終極典藏版】路徑分析工具 ---")

        if not ORS_API_KEY:
            print("\n!!! 嚴重錯誤：找不到 ORS_API_KEY !!!"); sys.exit(1)
            
        with open(START_POINTS_FILE, 'r', encoding='utf-8') as f:
            start_points = json.load(f)
        facility_df = pd.DataFrame(start_points)
        
        fields_to_create = {}
        for sp in start_points:
            eng_cat = sp.get('eng_category')
            if eng_cat:
                eng_cat_short = re.sub(r'[^a-zA-Z0-9]', '', eng_cat)[:7]
                fields_to_create[eng_cat] = {"name_field": f"{eng_cat_short}_Na", "dist_field": f"{eng_cat_short}_D"}
        
        print("正在讀取並準備宗地資料...")
        driver = ogr.GetDriverByName('ESRI Shapefile')
        dataSource_read = driver.Open(SHP_FILE_PATH, 0)
        if dataSource_read is None: raise IOError(f"無法開啟 SHP 檔案: {SHP_FILE_PATH}")
        layer_read = dataSource_read.GetLayer()
        
        feature_data_list = [{'land_id': f.GetField(LAND_ID_FIELD), 'Centroid_X': f.GetField(CENTROID_X_FIELD), 'Centroid_Y': f.GetField(CENTROID_Y_FIELD), '_internal_fid': f.GetFID()} for f in layer_read]
        report_df = pd.DataFrame(feature_data_list)
        dest_points_twd97 = [[d['Centroid_X'], d['Centroid_Y']] for d in feature_data_list]
        dest_points_wgs84 = transform_coords(dest_points_twd97, SRC_EPSG, DEST_EPSG)
        dataSource_read = None

        print("正在準備 SHP 檔案結構...")
        dataSource_update = driver.Open(SHP_FILE_PATH, 1)
        layer_update = dataSource_update.GetLayer()
        existing_fields = [layer_update.GetLayerDefn().GetFieldDefn(i).GetName() for i in range(layer_update.GetLayerDefn().GetFieldCount())]
        for cat, field_pair in fields_to_create.items():
            if field_pair['name_field'] not in existing_fields:
                name_field_defn = ogr.FieldDefn(field_pair['name_field'], ogr.OFTString); name_field_defn.SetWidth(50); layer_update.CreateField(name_field_defn); print(f"  已新增欄位: {field_pair['name_field']}")
            if field_pair['dist_field'] not in existing_fields:
                dist_field_defn = ogr.FieldDefn(field_pair['dist_field'], ogr.OFTReal); dist_field_defn.SetWidth(12); dist_field_defn.SetPrecision(2); layer_update.CreateField(dist_field_defn); print(f"  已新增欄位: {field_pair['dist_field']}")
        dataSource_update = None

        for start_point in start_points:
            name, eng_category = start_point.get("name"), start_point.get("eng_category")
            
            if name == SKIP_KEYWORD:
                print(f"\n--- 正在處理佔位符: {start_point.get('category')} - {name} ---")
                if eng_category and fields_to_create.get(eng_category):
                    report_df[fields_to_create[eng_category]['dist_field']] = np.nan
                continue
            
            if not eng_category: print(f"警告：起點 '{name}' 缺少 'eng_category'，將跳過。"); continue
            print(f"\n--- 正在處理起點: {start_point.get('category')} - {name} ---")
            
            # 【V12 核心改動】恢復混合座標處理邏輯
            lon, lat = start_point.get("lon"), start_point.get("lat")
            twd97_x, twd97_y = start_point.get("twd97_x"), start_point.get("twd97_y")
            
            start_point_wgs84 = None
            if lon is not None and lat is not None:
                print("   偵測到 WGS84 經緯度座標，直接使用。")
                start_point_wgs84 = [lon, lat]
            elif twd97_x is not None and twd97_y is not None:
                print("   偵測到 TWD97 座標，正在進行轉換...")
                start_point_wgs84 = transform_coords([[twd97_x, twd97_y]], SRC_EPSG, DEST_EPSG)[0]
                print(f"      轉換完成: {start_point_wgs84}")
            else:
                print(f"   警告：起點 '{name}' 座標缺失，無法處理，跳過。")
                continue

            print("   正在呼叫 Matrix API 計算所有距離...")
            try:
                locations = [start_point_wgs84] + dest_points_wgs84
                body = {"locations": locations, "sources": [0], "destinations": list(range(1, len(locations))), "metrics": ["distance"], "units": "m"}
                headers = {'Authorization': ORS_API_KEY, 'Content-Type': 'application/json'}
                response = requests.post('https://api.openrouteservice.org/v2/matrix/driving-car', json=body, headers=headers, timeout=90)
                response.raise_for_status(); distances = response.json().get('distances')[0]
                print("      API 請求成功。")
            except requests.exceptions.RequestException as e:
                print(f"      錯誤：Matrix API 請求失敗，跳過此起點。原因: {e}"); continue
            
            target_fields = fields_to_create.get(eng_category)
            if not target_fields: continue

            report_df[target_fields['name_field']] = name
            report_df[target_fields['dist_field']] = [round(d, 2) if d is not None else np.nan for d in distances]
            
            print(f"   正在將結果回填至 SHP 欄位...")
            dataSource_update = driver.Open(SHP_FILE_PATH, 1)
            layer_update = dataSource_update.GetLayer()
            for i, data_row in enumerate(feature_data_list):
                feature = layer_update.GetFeature(data_row['_internal_fid'])
                feature.SetField(target_fields['name_field'], name)
                feature.SetField(target_fields['dist_field'], distances[i] if distances[i] is not None else -1)
                layer_update.SetFeature(feature)
            dataSource_update = None
            print("      SHP 欄位更新成功。")

            if GENERATE_GEOJSON_ROUTES:
                print(f"   正在產生樣本路線 GeoJSON...")
                valid_distances = [d for d in distances if d is not None]
                if valid_distances:
                    max_dist_index = distances.index(max(valid_distances))
                    farthest_point_wgs84 = dest_points_wgs84[max_dist_index]
                    route_geojson = get_route_geojson(start_point_wgs84, farthest_point_wgs84, ORS_API_KEY)
                    if route_geojson:
                        geojson_filename = os.path.join(OUTPUT_FOLDER, f"route_sample_{eng_category}.geojson")
                        with open(geojson_filename, 'w', encoding='utf-8') as f: json.dump(route_geojson, f, ensure_ascii=False, indent=2)
                        print(f"      已產生到最遠點的樣本路線: {geojson_filename}")

        print(f"\n--- 正在產生最終的整合報表 ---")
        report_df.drop(columns=['_internal_fid'], inplace=True, errors='ignore')
        
        with pd.ExcelWriter(CONSOLIDATED_REPORT_XLSX, engine='openpyxl') as writer:
            report_df.to_excel(writer, sheet_name='Distance_Analysis', index=False)
            facility_df.to_excel(writer, sheet_name='Facility_Points_List', index=False)
        print(f"所有分析結果已匯總至 Excel 檔案: {CONSOLIDATED_REPORT_XLSX}")
        
        report_df.to_json(CONSOLIDATED_REPORT_JSON, orient='records', indent=2, force_ascii=False)
        print(f"所有分析結果已匯總至 JSON 檔案: {CONSOLIDATED_REPORT_JSON}")

        print("\n--- 所有任務執行完畢！ ---")

    except Exception as e:
        print("\n!!!!!!!!!!!!!!! 程式發生未預期的嚴重錯誤 !!!!!!!!!!!!!!!")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()