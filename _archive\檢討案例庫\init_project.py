# ===================================================================
#  檔案名稱: init_project.py
#  功    能: 一鍵初始化整個專案的資料夾結構與範例設定檔。
# ===================================================================
from pathlib import Path
import json
import importlib

def main():
    print("--- 正在初始化專案結構 ---")
    base_dir = Path(".")

    # 1. 建立標準資料夾結構
    dirs_to_create = [
        "form_input/config",
        "form_input/templates",
        "form_results",
        "gis_data",
        "handlers"
    ]
    for d in dirs_to_create:
        (base_dir / d).mkdir(parents=True, exist_ok=True)
        print(f"已建立/確認資料夾: {d}")

    # 2. 生成主設定檔 project_config.json
    config_path = base_dir / "form_input/config/project_config.json"
    if not config_path.exists():
        config_data = {
            "project_info": {"案號": "PROJECT-001"},
            "paths": {
                "form_templates": "form_input/templates",
                "form_results": "form_results",
                "gis_data": "gis_data/徵收土地地籍謄本.shp"
            },
            "general_settings": {"sort_by_field": "Parcel_ID"}
        }
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=4)
        print(f"已生成預設主設定檔: {config_path}")

    # 3. 呼叫處理器生成一組範例檔案
    try:
        handler_name = "comparison_form_handler" # 以最複雜的表4為例
        base_name = "表4_比較法調查估價表"
        handler_module = importlib.import_module(f"handlers.{handler_name}")
        if hasattr(handler_module, "create_config_templates"):
            print(f"\n--- 正在生成一組 '{base_name}' 的範例設定檔 ---")
            handler_module.create_config_templates(base_name, "form_input/templates")
    except Exception as e:
        print(f"\n生成範例設定檔時出錯（可能是尚未建立處理器檔案）: {e}")

    print("\n--- 專案初始化完畢！---")
    print("下一步建議:")
    print("1. 將您的 .shp 相關檔案放入 'gis_data' 資料夾。")
    print("2. 根據您的需求修改 'form_input/config/project_config.json'。")
    print("3. 開始使用 'python setup_tool.py ...' 或 'python form_AutomationV2.py ...'。")

if __name__ == "__main__":
    main()