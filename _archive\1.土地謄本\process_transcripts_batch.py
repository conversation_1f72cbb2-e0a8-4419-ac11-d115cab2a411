# ==============================================================================
# 程式名稱: process_transcripts_batch.py
# 版本: 1.0.1
# 最後修訂日期: 2025-07-28
#
# 重要修訂事項:
# - 2025-07-28 (v1.0.1 - 最終精修版):
#   - 修正：克服「無法擷取」問題，提高所有核心欄位的解析準確性。
#     - 策略：_extract_field 通用清理改為「極度保守白名單」，只允許確定有效字符。
#     - 策略：各欄位後處理階段進行「多層次精洗」：
#       - 先應用 _clean_trailing_junk 清理文本末尾常見雜訊（頁碼、續次頁、單獨數字/字母等）。
#       - 再針對各欄位設計嚴格的專屬正規表示式模式捕獲，若不符合模式則強制設為「無法擷取」。
#       - 強化標示部_其他登記事項對地號、公文號的保留。
#       - 強化地價欄位在數字合併前清理雜訊。
#       - 強化所有權人、統一編號、出生日期等欄位的模式驗證。
#       - 精確重構管理者資訊的提取邏輯，確保從住址中正確分離。
#   - 修正：地號、公文號消失，及其他欄位（如地價後）雜訊殘留問題。
#     - 策略：見上述 v1.0.1 修正細節。
# ==============================================================================

import pdfplumber
import re
import pandas as pd
import os
from pathlib import Path
import json
from typing import Dict, List, Any

# OCR 相關庫
import pytesseract
from PIL import Image # pytesseract 需要 Pillow (PIL fork) 庫

# 如果 Tesseract-OCR 執行檔不在系統PATH中，請取消註解並修改以下路徑
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'


# 用於寫入 Excel 檔案，需要先安裝: pip install openpyxl pandas
import openpyxl
from openpyxl.styles import Font, Alignment
from openpyxl.utils import get_column_letter
from itertools import groupby
from operator import itemgetter
import math

# --- 常數定義 ---

FULL_TO_HALF = str.maketrans(
    "０１２３４５６７８９ＡＢＣＤＥＦＧＨＩＪＫＬＭＮＯＰＱＲＳＴＵＶＷＸＹＺａｂｃｄｅｆｇｈｉｊｋｌｍｎｏｐｑｒｓｔｕｖｗｘｙｚ：－（）／",
    "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz:-()/"
)

LABEL_CANNOT_EXTRACT = "無法擷取"
LABEL_IS_BLANK = "(空白)"
LABEL_NOT_APPLICABLE = "此項無資料"

ORDERED_LAND_FIELDS = ["登記日期", "登記原因", "地    目", "等    則", "面    積", "使用分區", "使用地類別", "公告土地現值", "地上建物建號", "其他登記事項"]
ORDERED_OWNER_FIELDS = ["登記日期", "登記原因", "原因發生日期", "所有權人", "統一編號", "出生日期", "住    址", "權利範圍", "權狀字號", "當期申報地價", "前次移轉現值或原規定地價", "歷次取得權利範圍", "其他登記事項"]

FINAL_OWNER_KEYS = [
    "登記次序", "登記日期", "登記原因", "原因發生日期", "所有權人", "統一編號", "出生日期", "住址",
    "管理者", "管理者統一編號", "管理者住址",
    "權利範圍", "權狀字號", "當期申報地價", "前次移轉現值或原規定地價", 
    "歷次取得權利範圍", "相關他項權利登記次序", "其他登記事項"
]

CSV_COLUMN_ORDER = [
    "宗地流水號", "地段", "地號", "資料查詢時間",
    "標示部_登記日期", "標示部_登記原因", "標示部_地目", "標示部_等則", "標示部_面積",
    "標示部_使用分區", "標示部_使用地類別", "標示部_公告土地現值", "標示部_地上建物建號", "標示部_其他登記事項",
    "所有權部_登記次序", "所有權部_登記日期", "所有權部_登記原因", "所有權部_原因發生日期",
    "所有權部_所有權人", "所有權部_統一編號", "所有權部_出生日期", "所有權部_住址",
    "所有權部_管理者", "所有權部_管理者統一編號", "所有權部_管理者住址",
    "所有權部_權利範圍", "所有權部_權狀字號", "所有權部_當期申報地價",
    "所有權部_前次移轉現值或原規定地價", "所有權部_歷次取得權利範圍", "所有權部_相關他項權利登記次序",
    "所有權部_其他登記事項", "資料顯示完畢", "來源檔案"
]

def _extract_field(block_text: str, field_label: str, stop_keywords: List[str]) -> str:
    label_pattern = r'\s*'.join(field_label.split())
    stop_patterns = [r'\s*'.join(s.split()) for s in stop_keywords]
    stop_pattern = "|".join(stop_patterns) if stop_keywords else '(?!)'
    pattern = re.compile(rf"{label_pattern}\s*[:：]?\s*(.*?)(?=\s*(?:{stop_pattern}|$))", re.DOTALL)
    match = pattern.search(block_text)
    if match:
        raw_captured_group = match.group(1)
        # 【最終修正】通用清理：移除明確的噪音符號，並統一空格，保留必要標點
        # 僅移除非常確定的噪音符號，讓其他字符在後處理階段處理
        cleaned_text = re.sub(r'[\*\(\)=;_\?!]', '', raw_captured_group) # 移除非常確定的噪音符號
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip() # 標準化所有空白符
        
        value = cleaned_text.translate(FULL_TO_HALF)
        return value if value else LABEL_IS_BLANK
    return LABEL_CANNOT_EXTRACT

# 【新增】輔助函式：清理字串末尾的常見 OCR 噪音
def _clean_trailing_junk(text: str) -> str:
    if not isinstance(text, str): return text
    
    # 注意順序：從最特殊到最通用
    patterns_to_strip = [
        r'\s*(?:資料顯示完畢|頁次[:：]?\s*\d+)\s*$', # "資料顯示完畢", "頁次:X"
        r'\s*\(續次頁\).*$', # "(續次頁)"及其後所有內容
        r'\s+(?:[A-Z]\d*|\d+[A-Z]?)(?:\s+[A-Z]\d*|\s+\d+[A-Z]?)*$', # 像 "00", "4A", "B5", "E 1", "78", "9F" 等單獨的數字/字母或混合
        r'\s+\d+(?:\s+\d+)*$', # 連續的數字串（可能是OCR錯誤分隔的頁碼或無關數據）
        r'\s+(?:土地|地號|號)$' # " 土地", " 地號", " 號"
    ]
    for pattern in patterns_to_strip:
        text = re.sub(pattern, '', text, flags=re.DOTALL).strip()
    
    # 針對 "(空白) XX < >" 這種情況，如果清理後只剩這些則變為 LABEL_IS_BLANK
    if re.fullmatch(r'\(空白\)(?:\s+[^<>]+)*\s*(?:< *>)?', text):
        text = LABEL_IS_BLANK

    return text

def _format_price_field(price_str: str) -> str:
    price_str = _clean_trailing_junk(price_str) # 先清理尾部雜訊
    if not isinstance(price_str, str) or price_str in [LABEL_CANNOT_EXTRACT, LABEL_IS_BLANK]: return price_str
    
    price_str = re.sub(r'(\d)\s+(\d)', r'\1\2', price_str) # 合併數字間的空格
    pattern = re.compile(r'(?:民國)?(\d+年\d+月)\s*([\d,.]+元/平方公尺)')
    match = pattern.search(price_str)
    if match: return f"{match.group(1).replace(' ', '')} {match.group(2)}"
    
    fallback_match = re.search(r'([\d,.]+元/平方公尺)', price_str)
    if fallback_match: return fallback_match.group(1).strip()
    return LABEL_CANNOT_EXTRACT

def parse_land_transcript(pdf_path: Path):
    full_text_pages = []

    try:
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text_plumber = page.extract_text(x_tolerance=2, y_tolerance=2, layout=True)
                
                processed_page_text = ""
                if not page_text_plumber or len(page_text_plumber.strip()) < 50: 
                    print(f"  [OCR] 頁面 {page.page_number} 文字不足，嘗試 OCR...")
                    try:
                        page_image = page.to_image(resolution=300).original
                        processed_page_text = pytesseract.image_to_string(page_image, lang='chi_tra', config='--psm 6')
                        if not processed_page_text.strip():
                            print(f"  [OCR] 頁面 {page.page_number} OCR 辨識結果為空，回退 pdfplumber 結果。")
                            processed_page_text = page_text_plumber
                        else:
                            processed_page_text = re.sub(r'\s+', ' ', processed_page_text).strip()
                            processed_page_text = processed_page_text.translate(FULL_TO_HALF)
                    except Exception as ocr_e:
                        print(f"  [OCR錯誤] 頁面 {page.page_number} 執行 OCR 失敗: {ocr_e}，回退 pdfplumber 結果。")
                        processed_page_text = page_text_plumber
                else:
                    processed_page_text = page_text_plumber
                
                full_text_pages.append(processed_page_text)
        full_text = "\n".join(full_text_pages)

    except Exception as e:
        print(f"  [錯誤] 讀取 PDF 檔案 '{pdf_path.name}' 時發生錯誤: {e}")
        return []

    transcript_chunks = re.finditer(r"土\s*地\s*建\s*物\s*查\s*詢\s*資\s*料.*?〈\s*資料顯示完畢\s*〉", full_text, re.DOTALL)
    all_results_from_pdf = []

    for match in transcript_chunks:
        chunk = match.group(0)
        header_match = re.search(r"(?P<district>\S+段)\s+(?P<land_number>[\d-]+)地號\s*資料查詢時間：(?P<query_time>.*?)\s*頁次：", chunk, re.DOTALL)
        if not header_match: continue
        
        header_data = header_match.groupdict()
        data_complete_marker = "是" if re.search(r'〈\s*資料顯示完畢\s*〉', chunk) else "否"

        land_info = {}
        land_description_match = re.search(r"土地標示部(.*?(?=所有權部|他項權利部|$))", chunk, re.DOTALL)
        if land_description_match:
            land_description_text = land_description_match.group(1)
            for i, field in enumerate(ORDERED_LAND_FIELDS):
                stop_keywords = [f for f in ORDERED_LAND_FIELDS if f != field]
                land_info[field.replace(" ", "")] = _extract_field(land_description_text, field, stop_keywords)
            
            # 處理標示部特定欄位後處理
            category_val, price_val = land_info.get("使用地類別", ""), land_info.get("公告土地現值", "")
            date_pattern = r"(民國\s*\d+\s*年\s*\d+\s*月)"
            if (date_match := re.search(date_pattern, category_val)) and not re.search(date_pattern, price_val):
                date_str = date_match.group(1)
                cleaned_category = category_val.replace(date_str, "").strip()
                land_info["使用地類別"] = cleaned_category if cleaned_category else LABEL_IS_BLANK
                land_info["公告土地現值"] = f"{date_str.replace(' ', '')} {price_val}"
            land_info["公告土地現值"] = _format_price_field(land_info["公告土地現值"])
            
            value = land_info.get("其他登記事項", "")
            # 【最終修正】清理標示部其他登記事項的雜訊，防止過度移除地號/函號
            value = _clean_trailing_junk(value) # 先通用清理尾部雜訊
            
            # 針對 "重測前:塔子腳段0057-0030地號" 這種模式，提取地號
            re測_match = re.search(r'(重測前[:：]?\s*[\u4e00-\u9fa5A-Za-z\s.-]+(?:[0-9A-Z]+(?:-[0-9A-Z]+)*)?(?:地號|號))', value)
            if re測_match:
                value = re測_match.group(0).strip() # 只保留匹配到的部分
            
            # 針對 "(一般註記事項)依據桃園市政府105年1月14日府地用字第1050004021號函核准使用分區第一次劃定及使用地編定"
            general_note_match = re.search(r'(一般註記事項)(依據桃園市政府.*?函核准(?:使用分區第一次劃定及使用地編定)?)', value)
            if general_note_match:
                value = f"{general_note_match.group(1)}{general_note_match.group(2).replace(' ', '').replace(':', '：')}"
            
            land_info["其他登記事項"] = value if value else LABEL_IS_BLANK

        ownership_parts = []
        ownership_sections = re.finditer(r"所有權部(.*?(?=(?:所有權部|他項權利部|$)))", chunk, re.DOTALL)
        for section_match in ownership_sections:
            section_text = section_match.group(1)
            for block in re.split(r'(?=\s*（\d+）登記次序)', section_text)[1:]:
                if not block.strip(): continue
                owner_info = {}
                reg_order_match = re.search(r"（\d+）登記次序\s*[:：]\s*(\S+)", block)
                owner_info["登記次序"] = reg_order_match.group(1) if reg_order_match else LABEL_CANNOT_EXTRACT
                
                for i, field in enumerate(ORDERED_OWNER_FIELDS):
                    stop_keywords = [f for f in ORDERED_OWNER_FIELDS if f != field]
                    owner_info[field.replace(" ","")] = _extract_field(block, field, stop_keywords)
                
                # --- 【最終修正】所有權部欄位後處理與嚴格驗證 ---
                # 1. 登記原因：只保留第一個詞，並清理雜訊
                if "登記原因" in owner_info: owner_info["登記原因"] = _clean_trailing_junk(owner_info["登記原因"]).split(' ')[0].strip()

                # 2. 原因發生日期：嚴格匹配日期格式，清理尾隨雜訊
                cause_date = _clean_trailing_junk(owner_info.get("原因發生日期", LABEL_CANNOT_EXTRACT))
                if cause_date not in [LABEL_CANNOT_EXTRACT, LABEL_IS_BLANK]:
                    date_match = re.search(r'(民國\d+年\d+月\d+日)', cause_date)
                    owner_info["原因發生日期"] = date_match.group(1) if date_match else LABEL_CANNOT_EXTRACT
                
                # 3. 所有權人：只保留中文和英文名字
                owner_name = _clean_trailing_junk(owner_info.get("所有權人", LABEL_CANNOT_EXTRACT))
                if owner_name not in [LABEL_CANNOT_EXTRACT, LABEL_IS_BLANK]:
                    name_match = re.search(r'^([\u4e00-\u9fa5A-Za-z\s.-]+)', owner_name) # 使用捕獲組
                    if name_match:
                        owner_info["所有權人"] = name_match.group(1).strip()
                    else:
                        owner_info["所有權人"] = LABEL_CANNOT_EXTRACT
                
                # 4. 統一編號：只保留數字和字母
                uni_id = _clean_trailing_junk(owner_info.get("統一編號", LABEL_CANNOT_EXTRACT))
                if uni_id not in [LABEL_CANNOT_EXTRACT, LABEL_IS_BLANK]:
                    id_match = re.search(r'^([A-Z]?[0-9A-Z]{7,10})$', uni_id) # 嚴格匹配身份證或公司統編，去除後續內容
                    owner_info["統一編號"] = id_match.group(1).strip() if id_match else LABEL_CANNOT_EXTRACT

                # 5. 出生日期：嚴格匹配日期格式
                birth_date = _clean_trailing_junk(owner_info.get("出生日期", LABEL_CANNOT_EXTRACT))
                if birth_date not in [LABEL_CANNOT_EXTRACT, LABEL_IS_BLANK]:
                    date_match = re.search(r'(民國\d+年\d+月\d+日)', birth_date)
                    owner_info["出生日期"] = date_match.group(1) if date_match else LABEL_CANNOT_EXTRACT
                
                # 6. 權利範圍：確保格式正確
                if "權利範圍" in owner_info:
                    owner_info["權利範圍"] = _clean_trailing_junk(owner_info["權利範圍"])
                    if (share_match := re.search(r'^\s*(\S*分之\S*|全部)$', owner_info["權利範圍"])): owner_info["權利範圍"] = share_match.group(1)
                
                # 7. 權狀字號
                if "權狀字號" in owner_info: 
                    owner_info["權狀字號"] = _clean_trailing_junk(owner_info["權狀字號"])
                    owner_info["權狀字號"] = owner_info["權狀字號"].replace("---(空白)", "空白").strip()
                    if owner_info["權狀字號"] == "空白字第號": owner_info["權狀字號"] = LABEL_IS_BLANK 

                # 8. 地價資訊 (當期申報地價, 前次移轉現值或原規定地價) - 已在 _format_price_field 內部處理通用雜訊
                owner_info["當期申報地價"] = _format_price_field(owner_info.get("當期申報地價"))
                owner_info["前次移轉現值或原規定地價"] = _format_price_field(owner_info.get("前次移轉現值或原規定地價"))

                # 9. 歷次取得權利範圍與相關他項權利登記次序
                value = _clean_trailing_junk(owner_info.get("歷次取得權利範圍", LABEL_CANNOT_EXTRACT))
                # 確保只保留核心持分部分，移除所有後續數字或其他雜訊
                core_share_match = re.search(r'^(全部\s*\d*分之\d*|\d+\s*分之\d+)', value)
                if core_share_match:
                    value = core_share_match.group(0).strip()
                else:
                    value = LABEL_IS_BLANK # 如果沒有核心持分部分，則清空
                
                if (other_rights_match := re.search(r'(.*?)(?:相關他項權利登記次序\s*[:：]\s*(\S+))', value, re.DOTALL)):
                    owner_info['歷次取得權利範圍'], owner_info['相關他項權利登記次序'] = other_rights_match.group(1).strip(), other_rights_match.group(2).strip()
                else:
                    owner_info['歷次取得權利範圍'] = value # 可能是只有權利範圍沒有相關他項
                    owner_info['相關他項權利登記次序'] = LABEL_IS_BLANK
                
                # 10. 其他登記事項
                value = _clean_trailing_junk(owner_info.get("其他登記事項", LABEL_CANNOT_EXTRACT))
                value = re.sub(r'(\d)\s+(\d)', r'\1\2', value) # 合併數字間的空格
                value = re.sub(r'(辦理公有土地權利登記)[\s\S]*', r'\1', value) # 清理 "辦理公有土地權利登記" 後的雜訊
                if value == '': value = LABEL_IS_BLANK
                owner_info["其他登記事項"] = value
                
                # 11. 管理者資訊 (從住址欄位中獨立、更彈性地解析)
                raw_addr_text = owner_info.get("住址", "")
                manager_regex = re.compile(
                    r'(?P<pre_addr>.*?)(?:管\s*理\s*者\s*[:：]?\s*(?P<name>[\u4e00-\u9fa5A-Za-z\s.-]+)' # 捕獲管理者姓名
                    r'(?:\s*統一編號\s*[:：]?\s*(?P<id>[\dA-Z]+(?:-[\dA-Z]+)*))?' # 捕獲統一編號，可選
                    r'(?:\s*住\s*址\s*[:：]?\s*(?P<addr>.*))?)', # 捕獲管理者地址，可選
                    re.DOTALL
                )
                manager_match = manager_regex.search(raw_addr_text)

                if manager_match and manager_match.group('name') is not None:
                    owner_info['管理者'] = manager_match.group('name').strip() or LABEL_IS_BLANK
                    manager_id_val = manager_match.group('id').strip() if manager_match.group('id') else LABEL_IS_BLANK
                    manager_addr_val = manager_match.group('addr').strip() if manager_match.group('addr') else LABEL_IS_BLANK

                    owner_info['管理者統一編號'] = manager_id_val
                    owner_info['管理者住址'] = manager_addr_val
                    
                    cleaned_owner_address = manager_match.group('pre_addr').strip()
                    owner_info['住址'] = cleaned_owner_address if cleaned_owner_address else LABEL_IS_BLANK
                else:
                    owner_info['管理者'], owner_info['管理者統一編號'], owner_info['管理者住址'] = LABEL_NOT_APPLICABLE, LABEL_NOT_APPLICABLE, LABEL_NOT_APPLICABLE
                    owner_info['住址'] = raw_addr_text # 保持原始提取的住址，如果沒有管理者信息

                ownership_parts.append({key: owner_info.get(key, LABEL_NOT_APPLICABLE) for key in FINAL_OWNER_KEYS})
        all_results_from_pdf.append({"地段": header_data.get("district", LABEL_CANNOT_EXTRACT), "地號": header_data.get("land_number", LABEL_CANNOT_EXTRACT),"資料查詢時間": " ".join(header_data.get("query_time", LABEL_CANNOT_EXTRACT).split()), "土地標示部": land_info,"所有權部": ownership_parts, "資料顯示完畢": data_complete_marker, "來源檔案": pdf_path.name})
    return all_results_from_pdf

def create_final_table(data_list: List[Dict[str, Any]]) -> pd.DataFrame:
    records = []
    for data in data_list:
        if not data: continue
        common_info = {k: data.get(k) for k in ["宗地流水號", "地段", "地號", "資料查詢時間", "資料顯示完畢", "來源檔案"]}
        common_info.update({f"標示部_{k}": v for k, v in data.get("土地標示部", {}).items()})
        if not data.get("所有權部"): records.append(common_info)
        else:
            for owner_part in data["所有權部"]:
                records.append({**common_info, **{f"所有權部_{k}": v for k, v in owner_part.items()}})
    if not records: return pd.DataFrame()
    return pd.DataFrame(records).reindex(columns=CSV_COLUMN_ORDER)

def _split_district(full_district_str: str) -> (str, str):
    match = re.match(r'(.*?(?:市|區|鎮|鄉))(.*)', full_district_str)
    if match: return match.group(1).strip(), match.group(2).strip()
    return "未知行政區", full_district_str.strip()

def _format_share(share_str: str) -> str:
    if not isinstance(share_str, str): return "格式錯誤"
    fraction_match = re.search(r'(\d+)\s*分之\s*(\d+)', share_str)
    if fraction_match: return f"{fraction_match.group(2)}/{fraction_match.group(1)}"
    if "全部" in share_str: return "1/1"
    return share_str.strip()

def _write_printable_sheet(ws, district_records, land_summary_map, project_name):
    ROWS_PER_CHUNK, COLS_PER_DATA_BLOCK, BLOCKS_PER_ROW = 25, 3, 4
    PAGE_ROW_GAP, HEADER_ROWS, MAX_ROWS_PER_PAGE = 2, 5, 34
    
    font_bold_large = Font(name='標楷體', bold=True, size=16)
    font_bold_normal = Font(name='標楷體', bold=True, size=12)
    font_normal = Font(name='標楷體', size=11)
    align_center = Alignment(horizontal='center', vertical='center')
    align_left = Alignment(horizontal='left', vertical='center')

    for i in range(BLOCKS_PER_ROW):
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 1)].width = 5
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 2)].width = 16
        ws.column_dimensions[get_column_letter(i * COLS_PER_DATA_BLOCK + 3)].width = 12

    plots_data = {k: list(v) for k, v in groupby(district_records, key=itemgetter('宗地流水號'))}
    plot_keys = sorted(plots_data.keys())
    
    current_page_start_row, current_col_offset = 1, 0
    row_heights_in_current_page_row = [0]

    def write_page_headers(start_row):
        ws.merge_cells(start_row=start_row, start_column=1, end_row=start_row, end_column=12)
        cell = ws.cell(start_row, 1, "共有土地共有人及對應持分附表"); cell.font = font_bold_large; cell.alignment = align_center
        ws.merge_cells(start_row=start_row+1, start_column=1, end_row=start_row+1, end_column=12)
        cell = ws.cell(start_row+1, 1, project_name); cell.font = font_normal; cell.alignment = align_left
        return start_row + 3

    current_row = write_page_headers(current_page_start_row)

    for serial_no in plot_keys:
        plot_records = plots_data[serial_no]
        if not plot_records: continue
        
        num_chunks = math.ceil(len(plot_records) / ROWS_PER_CHUNK)
        rows_needed_for_block = HEADER_ROWS + ROWS_PER_CHUNK
        
        if current_col_offset > 0 and (current_col_offset + COLS_PER_DATA_BLOCK * num_chunks > COLS_PER_DATA_BLOCK * BLOCKS_PER_ROW):
             current_row += max(row_heights_in_current_page_row) + PAGE_ROW_GAP
             current_col_offset, row_heights_in_current_page_row = 0, [0]

        if current_row - current_page_start_row + rows_needed_for_block > MAX_ROWS_PER_PAGE:
            current_page_start_row = ws.max_row + PAGE_ROW_GAP + 1 if ws.max_row > 1 else MAX_ROWS_PER_PAGE
            current_row = write_page_headers(current_page_start_row)
            current_col_offset, row_heights_in_current_page_row = 0, [0]
            
        start_col = 1 + current_col_offset
        record_sample = plot_records[0]

        for i, label in enumerate(["宗地流水號", "鄉鎮市區", "段小段名稱", "地號"]):
            ws.cell(current_row + i, start_col, label).font = font_bold_normal
            ws.merge_cells(start_row=current_row + i, start_column=start_col + 1, end_row=current_row + i, end_column=start_col + COLS_PER_DATA_BLOCK * num_chunks - 1)
            ws.cell(current_row + i, start_col + 1, record_sample.get(label, "")).font = font_normal if i > 0 else font_bold_normal

        data_start_row = current_row + HEADER_ROWS

        for i, record in enumerate(plot_records):
            chunk_idx, row_in_chunk = divmod(i, ROWS_PER_CHUNK)
            col_offset_in_block = chunk_idx * COLS_PER_DATA_BLOCK
            if row_in_chunk == 0:
                summary_text = land_summary_map.get((record['地段'], record['地號']), '')
                for j, sub_header in enumerate(["土地所有權人或管理人", summary_text, "持分"]):
                    ws.cell(data_start_row - 1, start_col + col_offset_in_block + j, sub_header).font = font_bold_normal
            
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block, i + 1).font = font_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block + 1, record['土地所有權人或管理人']).font = font_normal
            ws.cell(data_start_row + row_in_chunk, start_col + col_offset_in_block + 2, record['持分']).font = font_normal

        current_col_offset += COLS_PER_DATA_BLOCK * num_chunks
        row_heights_in_current_page_row.append(rows_needed_for_block)


def create_excel_report(data_list: List[Dict[str, Any]], output_path: Path, project_name: str):
    if not data_list: return
    print(f"--- 正在為 {output_path.name} 產生報告 ---")
    
    all_records = []
    for lp in data_list:
        if lp.get("所有權部"):
            for oi in lp["所有權部"]:
                person_in_charge = oi.get("管理者") if oi.get("管理者") not in [None, LABEL_NOT_APPLICABLE, ""] else oi.get("所有權人", LABEL_CANNOT_EXTRACT)
                all_records.append({
                    '地段': lp.get("地段"),
                    '地號': lp.get("地號"),
                    '登記次序': oi.get("登記次序"),
                    '宗地流水號': lp.get("宗地流水號"),
                    '土地所有權人或管理人': person_in_charge,
                    '持分_原始': oi.get("權利範圍")
                })
    
    if not all_records: print(f"  [注意] 檔案 {output_path.name} 沒有可用於產生附表的有效所有權人資料。"); return

    with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
        district_records_list = sorted(all_records, key=lambda r: (r['地段'], r['地號'], int(str(r.get('登記次序', '0')).split('(')[0]) if str(r.get('登記次序', '0')).split('(')[0].isdigit() else 9999))
        
        land_summary_map = {}
        for land_id, group in groupby(district_records_list, key=lambda r: (r['地段'], r['地號'])):
            group_list = list(group)
            if not group_list: continue 
            count = len(group_list)
            first_owner_name = group_list[0].get('土地所有權人或管理人', LABEL_CANNOT_EXTRACT) 
            land_summary_map[land_id] = f"{first_owner_name} 等{count}人" if count > 1 else first_owner_name
        
        for record in district_records_list:
            record['鄉鎮市區'], record['段小段名稱'] = _split_district(record['地段'])
            record['持分'] = _format_share(record['持分_原始'])
        
        appendix_rows, headers = [], ["宗地流水號", "鄉鎮市區", "段小段名稱", "地號", "登記次序", "土地所有權人或管理人", "持分", "有權人與持分彙總"]
        processed_summaries = set()
        for record in district_records_list:
            land_id = (record['地段'], record['地號'])
            summary = land_summary_map.get(land_id, "") if land_id not in processed_summaries else ""
            if summary: processed_summaries.add(land_id)
            appendix_rows.append([record['宗地流水號'], record['鄉鎮市區'], record['段小段名稱'], record['地號'], record['登記次序'], record['土地所有權人或管理人'], record['持分'], summary])
        df_appendix = pd.DataFrame(appendix_rows, columns=headers)
        
        df_transcript = create_final_table(data_list)
        
        df_transcript['總共有人數'] = df_transcript.apply(lambda row: land_summary_map.get((row['地段'], row['地號']), LABEL_NOT_APPLICABLE), axis=1)
        df_transcript = df_transcript.reindex(columns=[col for col in df_transcript.columns if col != '總共有人數'] + ['總共有人數'])

        df_transcript.to_excel(writer, sheet_name='各地段土地謄本', index=False)
        df_appendix.to_excel(writer, sheet_name='各地段對應持分附表', index=False)
        
        wb = writer.book
        ws_printable = wb.create_sheet("各地段對應持分附表1")
        _write_printable_sheet(ws_printable, district_records_list, land_summary_map, project_name)

    print(f"📊 已產生整合式 Excel 報告: {output_path.name}")

def main():
    project_dir, input_dir, output_dir = Path(__file__).resolve().parent, Path("input_pdfs"), Path("output")
    input_dir.mkdir(exist_ok=True); output_dir.mkdir(exist_ok=True)
    pdf_files = list(input_dir.glob("*.pdf"))
    if not pdf_files: print(f"錯誤：在資料夾 '{input_dir}' 中找不到 PDF。請將檔案複製到該資料夾。"); return

    print(f"找到 {len(pdf_files)} 個 PDF 檔案，開始處理...")
    all_parsed_data, successful_files = [], 0
    project_name_for_report = "興辦事業計畫名稱：桃園市桃園區龍泉三街至大慶街(兒28周邊道路西側)用地取得"

    for pdf_path in pdf_files:
        print(f"\n--- 正在處理: {pdf_path.name} ---")
        try:
            parsed_data = parse_land_transcript(pdf_path)
            if not parsed_data: print(f"  [注意] 未能從 '{pdf_path.name}' 中解析出任何資料。"); continue
            all_parsed_data.extend(parsed_data)
            successful_files += 1
        except Exception as e:
            print(f"  [嚴重錯誤] 處理 '{pdf_path.name}' 時發生未預期的錯誤: {e}")

    if not all_parsed_data: print("\n處理完成，但未能從任何檔案中成功解析資料。"); return

    # --- 全局流水號生成 (針對所有地號) ---
    unique_plots = sorted(list(set((d['地段'], d['地號']) for d in all_parsed_data)))
    serial_map = {plot_key: f"{i+1:04d}" for i, plot_key in enumerate(unique_plots)}
    for item in all_parsed_data: item['宗地流水號'] = serial_map.get((item['地段'], item['地號']))

    # --- 輸出獨立報告 (按地段分組，每個地段一份完整的 JSON, CSV, XLSX) ---
    all_parsed_data.sort(key=itemgetter('地段')) # 確保 groupby 能正確分組
    for district, group in groupby(all_parsed_data, key=itemgetter('地段')):
        district_data = list(group)
        if not district_data: continue

        base_name = district.replace(" ", "")
        
        df_single = create_final_table(district_data)
        df_single.to_csv(output_dir / f"{base_name}.csv", index=False, encoding='utf-8-sig')
        with open(output_dir / f"{base_name}.json", 'w', encoding='utf-8') as f: json.dump(district_data, f, ensure_ascii=False, indent=4)
        create_excel_report(district_data, output_dir / f"土地清冊及持分附表--{base_name}.xlsx", project_name_for_report)
        print(f"  -> 已為地段 '{district}' 產生獨立報告 (JSON, CSV, XLSX)。")

    # --- 輸出合併報告 (如果處理了多個檔案) ---
    if successful_files > 1:
        print("\n--- 正在產生合併報告 ---")
        
        final_df_consolidated = create_final_table(all_parsed_data)
        final_df_consolidated.to_csv(output_dir / "consolidated_land_data.csv", index=False, encoding='utf-8-sig')
        print(f"✅ 合併的 CSV 檔案已儲存。")
        
        with open(output_dir / "consolidated_land_data.json", 'w', encoding='utf-8') as f:
            json.dump(all_parsed_data, f, ensure_ascii=False, indent=4)
        print(f"📝 合併的 JSON 診斷檔案已儲存。")
        
        create_excel_report(all_parsed_data, output_dir / "consolidated_land_data.xlsx", project_name_for_report)
    
    print("\n" + "="*50)
    print("✅ 處理完成！")
    print(f"總共掃描 {len(pdf_files)} 個 PDF，成功處理 {successful_files} 個。")
    print(f"總共解析 {len(all_parsed_data)} 個地號區塊，產生 {len(create_final_table(all_parsed_data))} 筆記錄。")
    print("="*50)

if __name__ == "__main__":
    main()