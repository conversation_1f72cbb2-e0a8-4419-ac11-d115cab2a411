# ===================================================================
#  檔案名稱: handlers/utils.py
#  版    本: v1.0
#  說    明: 專案共享輔助函式模組。
#            - 集中管理通用功能，以減少程式碼重複。
#            - 統一並強化核心邏輯 (如：儲存格寫入、數值轉換)。
# ===================================================================
import pandas as pd
import logging
from typing import Optional, Any
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.cell import MergedCell

def write_to_cell(sheet: Worksheet, row: int, col: int, value: Any, logger: logging.Logger):
    """
    【強化版】通用儲存格寫入函式。
    - 能正確處理合併儲存格 (MergedCell)。
    - 當寫入失敗時，會透過 logger 記錄警告，而不是靜默忽略。
    """
    if not (isinstance(row, int) and row > 0 and isinstance(col, int) and col > 0):
        logger.warning(f"提供無效的儲存格座標: row={row}, col={col}。操作已跳過。")
        return

    try:
        cell = sheet.cell(row=row, column=col)
        # 判斷是否為合併儲存格的一部分
        if isinstance(cell, MergedCell):
            # 遍歷所有合併區域，找到包含此儲存格的區域
            for merged_range in sheet.merged_cells.ranges:
                if cell.coordinate in merged_range:
                    # 對該區域左上角的儲存格進行寫入
                    main_cell = sheet.cell(row=merged_range.min_row, column=merged_range.min_col)
                    main_cell.value = value
                    return
        # 如果不是合併儲存格，或就是左上角的儲存格
        cell.value = value
    except Exception as e:
        logger.warning(f"寫入儲存格 (R{row}, C{col}) 失敗: {e}")

def clean_and_convert_to_float(value: Any) -> Optional[float]:
    """
    通用函式：清理字串並嘗試轉換為浮點數。
    - 移除逗號、處理前後空白。
    - 支援百分比字串 (例如 '5.5%')。
    - 轉換失敗時回傳 None。
    """
    if value is None or pd.isna(value):
        return None
    if isinstance(value, (int, float)):
        return float(value)
    
    s_value = str(value).strip().replace(',', '')
    try:
        if '%' in s_value:
            return float(s_value.replace('%', '')) / 100.0
        return float(s_value)
    except (ValueError, TypeError):
        return None