# ===================================================================
#  檔案名稱: handlers/dynamic_form_handler.py
#  版    本: v5.5 - Final Pandas Access Fix
#  說    明: 修正對 Pandas Series 和 DataFrame 存取值的錯誤用法。
# ===================================================================
import pandas as pd
from typing import Dict, Any, Optional
import logging
from openpyxl.worksheet.worksheet import Worksheet
from openpyxl.utils import column_index_from_string
from openpyxl.utils.cell import coordinate_from_string

from .base import BaseHandler, HandlerContext
from . import utils

class DynamicFormHandler(BaseHandler):
    
    def _get_value_from_df(self, df_or_series: Any, column_name: str, default: Any = "") -> Any:
        # [修正] 處理可以是 DataFrame 或 Series 的情況
        if isinstance(df_or_series, pd.Series):
            if column_name in df_or_series.index: # 對於 Series 使用 .index 檢查欄位
                value = df_or_series[column_name]
                return value if pd.notna(value) else default
        elif isinstance(df_or_series, pd.DataFrame) and not df_or_series.empty:
            if column_name in df_or_series.columns: # 對於 DataFrame 使用 .columns 檢查欄位
                value = df_or_series.iloc[0][column_name] # 取第一行的值
                return value if pd.notna(value) else default
        return default

    def _rate_item(self, active_rules: pd.DataFrame, rule_type: str, raw_input_value: Any, logger: logging.Logger) -> tuple:
        score, score_text = "", ""
        if rule_type == 'CHOICE' and raw_input_value is not None:
            result = active_rules[active_rules['OptionText'].astype(str).str.strip() == str(raw_input_value).strip()]
            if not result.empty:
                score = self._get_value_from_df(result, 'ScoreLevel')
                score_text = self._get_value_from_df(result, 'ScoreText')
            else:
                logger.warning(f"     - [CHOICE匹配失敗] 輸入值 '{raw_input_value}' 在規則中找不到對應的 OptionText。")
        elif rule_type == 'NUMBER_RANGE' and pd.notna(raw_input_value):
            input_val = utils.clean_and_convert_to_float(raw_input_value)
            if input_val is None:
                logger.warning(f"     - [類型錯誤] 輸入值 '{raw_input_value}' 無法轉換為數字。"); return "", ""
            matched = False
            for _, rule_row in active_rules.iterrows():
                param1 = rule_row.get('Param1')
                if pd.notna(param1) and input_val <= float(param1):
                    score, score_text, matched = rule_row.get('ScoreLevel'), rule_row.get('ScoreText'), True; break
            if not matched:
                default_rule = active_rules[active_rules['Param1'].isna()]
                if not default_rule.empty:
                    score = self._get_value_from_df(default_rule, 'ScoreLevel')
                    score_text = self._get_value_from_df(default_rule, 'ScoreText')
            if score == "":
                logger.warning(f"     - [NUMBER_RANGE匹配失敗] 輸入值 '{input_val}' 未能匹配任何區間。")
        return score, score_text

    def process(self, context: HandlerContext) -> bool:
        logger = context.logger
        logger.info(f"--- 使用策略: 'DynamicFormHandler' v5.5 (Final Pandas Access Fix) ---") 
        
        target_id = context.target_id
        if not target_id:
            logger.error("ID 處理模式缺少必要的 target_id 參數。"); return False
        
        # --- 讀取設定檔 (增加彈性) ---
        mapping_df, rulebook_df, master_plan_df = None, pd.DataFrame(), pd.DataFrame()
        try:
            if context.mapping_path and context.mapping_path.exists():
                mapping_df = pd.read_excel(context.mapping_path, sheet_name='RuleBasedLogic', engine='openpyxl')
            else:
                logger.error(f"找不到必要的 mapping 檔案: {context.mapping_path}"); return False
            
            if context.rulebook_path and context.rulebook_path.exists():
                rulebook_df = pd.read_excel(context.rulebook_path, sheet_name='Rules', engine='openpyxl')
            else:
                logger.warning(f"未找到評分規則檔案 (Rulebook): {context.rulebook_path}，評分相關功能將被跳過。")
            
            if context.master_plan_path and context.master_plan_path.exists():
                master_plan_df = pd.read_excel(context.master_plan_path, engine='openpyxl')
            else:
                logger.warning(f"未找到基準表檔案 (MasterPlan): {context.master_plan_path}，將處理 mapping 中的所有項目。")
        except Exception as e:
            logger.error(f"-> 讀取設定檔時發生錯誤: {e}"); return False

        # --- 確定上下文 ---
        main_source_key = str(context.layout_config.get('main_data_source_key'))
        id_column_main = str(context.gis_sources_config.get(main_source_key, {}).get('id_column'))
        main_gdf = context.gdfs.get(main_source_key, pd.DataFrame())
        main_data_rows = main_gdf[main_gdf[id_column_main].astype(str) == target_id] if not main_gdf.empty and id_column_main else pd.DataFrame()
        if main_data_rows.empty:
            logger.error(f"在 '{main_source_key}' 中找不到 ID '{target_id}' 的資料。"); return False
        
        # [修正] 確保這裡得到的是 Series
        main_data_series = main_data_rows.iloc[0] 

        use_type_field = str(context.layout_config.get('use_type_field_in_shp'))
        current_use_type = str(main_data_series.get(use_type_field, "")).strip()
        if not current_use_type:
            logger.warning(f"無法確定評估上下文 UseType (欄位 '{use_type_field}' 為空或不存在)，將使用通用規則。")
            current_use_type = "通用" # 提供一個預設值

        calculated_scores: Dict[str, Any] = {}
        
        if not master_plan_df.empty:
            items_to_process = master_plan_df[master_plan_df['UseType'] == current_use_type]['ItemName'].tolist()
            logger.info(f"   - 根據基準表，將處理 {len(items_to_process)} 個項目。")
        else:
            items_to_process = mapping_df['ItemName'].unique().tolist()
            logger.info(f"   - 根據 Mapping 檔案，將處理 {len(items_to_process)} 個項目。")

        def write_by_coord(coord_str, value):
            if pd.notna(coord_str):
                try:
                    col_s, row_n = coordinate_from_string(str(coord_str))
                    utils.write_to_cell(context.sheet, row_n, column_index_from_string(col_s), value, logger)
                except ValueError: logger.warning(f"發現無效的儲存格座標 '{coord_str}'，已跳過。")
        
        # --- 階段一: 計算基礎項目 ---
        logger.info("--- [階段一] 開始計算所有基礎項目分數 ---")
        for _, mapping_rule in mapping_df.iterrows():
            item_name = str(mapping_rule.get('ItemName', '')).strip()
            rule_type = str(mapping_rule.get('RuleType', '')).strip()
            if not item_name or rule_type == 'AGGREGATE_AND_RATE': continue
            
            logger.info(f"   - 正在處理基礎項目: '{item_name}'")
            db_source_key = str(mapping_rule.get('DB_SourceKey', main_source_key))
            source_gis_field = str(mapping_rule.get('Source_GIS_Field', '')).strip()
            data_series = main_data_series # 這裡已經是 Series
            if db_source_key != main_source_key:
                other_gdf = context.gdfs.get(db_source_key, pd.DataFrame())
                other_id_col = str(context.gis_sources_config.get(db_source_key, {}).get('id_column', id_column_main))
                other_data_rows = other_gdf[other_gdf[other_id_col].astype(str) == target_id] if not other_gdf.empty else pd.DataFrame()
                # [修正] 確保這裡也得到的是 Series
                data_series = other_data_rows.iloc[0] if not other_data_rows.empty else pd.Series(dtype=object)
            
            raw_input_value = data_series.get(source_gis_field) if source_gis_field and source_gis_field in data_series.index else None
            if isinstance(raw_input_value, str): raw_input_value = raw_input_value.strip()
            logger.info(f"     - 從 GIS源 '{db_source_key}' 的欄位 '{source_gis_field}' 讀取到原始值: '{raw_input_value}'")
            write_by_coord(mapping_rule.get('Target_InputCell'), raw_input_value)
            
            score, score_text, scale = "", "", ""
            if not rulebook_df.empty:
                rule_alias = mapping_rule.get('RuleAlias_ItemName')
                rule_lookup_name = str(rule_alias).strip() if pd.notna(rule_alias) else item_name
                active_rules = rulebook_df[(rulebook_df['UseType'].astype(str).str.contains(current_use_type, na=False)) & (rulebook_df['ItemName'] == rule_lookup_name)]
                if not active_rules.empty:
                    scale = active_rules['ScoreLevel'].nunique()
                    score, score_text = self._rate_item(active_rules, rule_type, raw_input_value, logger)
                    if score != "" and str(score).isdigit(): calculated_scores[item_name] = score
                else: logger.info(f"     - 在 Rulebook 中找不到 UseType='{current_use_type}' 且 ItemName='{rule_lookup_name}' 的規則。")

            write_by_coord(mapping_rule.get('Target_ScaleCell'), scale)
            write_by_coord(mapping_rule.get('Target_ScoreCell'), score)
            write_by_coord(mapping_rule.get('Target_TextCell'), score_text)

        # --- 階段二: 計算綜合項目 ---
        logger.info("--- [階段二] 開始計算綜合評分項目 ---")
        for _, mapping_rule in mapping_df.iterrows():
            item_name, rule_type = str(mapping_rule.get('ItemName', '')).strip(), str(mapping_rule.get('RuleType', '')).strip()
            if rule_type != 'AGGREGATE_AND_RATE': continue
            
            logger.info(f"   - 正在處理綜合項目: '{item_name}'")
            sub_items_str = str(mapping_rule.get('SubItems', '')).strip()
            if not sub_items_str: logger.warning(f"     - 綜合項目 '{item_name}' 未定義 'SubItems'。"); continue
            sub_item_names = [s.strip() for s in sub_items_str.split(',')]
            valid_sub_scores = [int(calculated_scores[sub_item]) for sub_item in sub_item_names if sub_item in calculated_scores and str(calculated_scores.get(sub_item)).isdigit()]
            if not valid_sub_scores: logger.warning(f"     - 綜合項目 '{item_name}' 的所有子項目均無有效分數，無法聚合。"); continue
            
            total_score = sum(valid_sub_scores)
            logger.info(f"     - 子項目 {sub_item_names} 的聚合總分為: {total_score}")
            write_by_coord(mapping_rule.get('Target_InputCell'), total_score)
            
            agg_score, agg_score_text = "", ""
            if not rulebook_df.empty:
                rule_alias = mapping_rule.get('RuleAlias_ItemName')
                rule_lookup_name = str(rule_alias).strip() if pd.notna(rule_alias) else item_name
                active_rules = rulebook_df[(rulebook_df['UseType'].astype(str).str.contains(current_use_type, na=False)) & (rulebook_df['ItemName'] == rule_lookup_name)]
                if not active_rules.empty:
                    agg_score, agg_score_text = self._rate_item(active_rules, 'NUMBER_RANGE', total_score, logger)

            write_by_coord(mapping_rule.get('Target_ScoreCell'), agg_score)
            write_by_coord(mapping_rule.get('Target_TextCell'), agg_score_text)
            
        logger.info(f"已成功為 ID '{target_id}' 填寫動態表單。")
        return True