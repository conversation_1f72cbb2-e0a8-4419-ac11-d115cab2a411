{
  // ... 您其他的設定 ...

  // 啟用儲存時格式化
  "editor.formatOnSave": true,

  // 設定 Prettier 為預設格式化工具
  "editor.defaultFormatter": "esbenp.prettier-vscode",

  // 針對特定語言設定格式化工具 (更精準的作法)
  "[python]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
