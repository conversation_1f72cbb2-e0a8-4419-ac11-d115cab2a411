# ==============================================================================
# 程式名稱: lvr_land.py
# 版本: 4.8.3 (最終穩定版)
# 最後修訂日期: 2025-08-06
#
# 程式說明:
# ... (同 v4.8.1)
#
# 安裝依賴:
# pip install pandas geopandas openpyxl shapely thefuzz python-Levenshtein
#
# 版本歷史:
# - v4.8.3:
#   - [CRITICAL FIX] 修正 _generate_mismatch_report 中 pd.merge 的 KeyError，
#     因 df_report 的欄位已改為中文，需明確指定 left_on 和 right_on 參數。
# - v4.8.2:
#   - [優化] 重構模糊比對邏輯，改用「鄉鎮市區」預篩選，確保建議能正常產出。
# - v4.8.1:
#   - [優化] 統一地址清理邏輯，確保 data_key 與最終顯示的地址樣態一致性。
# ==============================================================================

import json
import pandas as pd
import geopandas
from shapely.geometry import Point
import re
import os
import sys
import traceback
from typing import Any, Tuple, Optional, List, Dict
from pathlib import Path
from pandas import ExcelWriter

try:
    from thefuzz import process as fuzz_process
except ImportError:
    print("警告：'thefuzz' 或 'python-Levenshtein' 函式庫未安裝。偵錯報告中的模糊比對功能將被禁用。")
    print("請執行 'pip install thefuzz python-Levenshtein' 來安裝。")
    fuzz_process = None

# --- 標準化輸出設定 ---
FIELD_DEFINITIONS = [
    {'zh': '(自訂) 資料索引', 'json': '(衍生)', 'xls': None, 'en': 'data_key', 'desc': '由地址、日期、總價組成的唯一識別碼'},
    {'zh': '(自訂) 縣市', 'json': 'city (衍生)', 'xls': None, 'en': 'City_Na', 'desc': '縣市名稱與代碼'},
    {'zh': '鄉鎮市區', 'json': 'town (衍生)', 'xls': '鄉鎮市區', 'en': 'Town_Na', 'desc': '鄉鎮市區名稱與代碼'},
    {'zh': '土地位置建物門牌', 'json': 'a', 'xls': '土地位置建物門牌', 'en': 'Address', 'desc': '基地坐落門牌或地號'},
    {'zh': '建物門牌', 'json': 'a (衍生)', 'xls': None, 'en': 'Bulid_Addr', 'desc': '清理後的建物門牌地址'},
    {'zh': '地段地籍', 'json': 'a (衍生)', 'xls': None, 'en': 'Land_Addr', 'desc': '清理後的地段地號地址'},
    {'zh': '(自訂) 地政事務所名稱', 'json': '(衍生)', 'xls': None, 'en': 'Land_Unit', 'desc': '地政事務所名稱'},
    {'zh': '(自訂) 地政事務所代碼', 'json': '(衍生)', 'xls': None, 'en': 'Land_UnID', 'desc': '地政事務所代碼'},
    {'zh': '(自訂) 地段名稱', 'json': '(衍生)', 'xls': None, 'en': 'Lnad_Sec', 'desc': '地段名'},
    {'zh': '(自訂) 地段代碼', 'json': '(衍生)', 'xls': None, 'en': 'Lnad_ScoID', 'desc': '地段代碼'},
    {'zh': '(自訂) 地號', 'json': '(衍生)', 'xls': None, 'en': 'Land_No08', 'desc': '地號(8碼)'},
    {'zh': '(自訂) 完整地籍編號', 'json': '(衍生)', 'xls': None, 'en': 'Land_No14', 'desc': '完整地籍編號(14碼)'},
    {'zh': '都市土地使用分區', 'json': None, 'xls': '都市土地使用分區', 'en': 'Urb_Zone', 'desc': '都市土地使用分區'},
    {'zh': '非都市土地使用分區', 'json': None, 'xls': '非都市土地使用分區', 'en': 'Nurb_Zone', 'desc': '非都市土地使用分區'},
    {'zh': '非都市土地使用編定', 'json': None, 'xls': '非都市土地使用編定', 'en': 'Nurb_ZoneC', 'desc': '非都市土地使用編定'},
    {'zh': '交易年月日', 'json': 'e', 'xls': '交易年月日', 'en': 'Trans_Date', 'desc': '民國年月日'},
    {'zh': '交易標的', 'json': 't', 'xls': '交易標的', 'en': 'Trans_Type', 'desc': '房地、土地、建物...'},
    {'zh': '交易筆棟數', 'json': None, 'xls': '交易筆棟數', 'en': 'Trans_Num', 'desc': '交易筆棟數'},
    {'zh': '移轉編號', 'json': 'id', 'xls': '移轉編號', 'en': 'Trans_ID', 'desc': '移轉編號'},
    {'zh': '土地移轉總面積平方公尺', 'json': None, 'xls': '土地移轉總面積平方公尺', 'en': 'Land_Sqm', 'desc': '土地移轉總面積(平方公尺)'},
    {'zh': '建物移轉總面積平方公尺', 'json': None, 'xls': '建物移轉總面積平方公尺', 'en': 'Bulid_Sqm', 'desc': '建物移轉總面積(平方公尺)'},
    {'zh': '車位移轉總面積平方公尺', 'json': None, 'xls': '車位移轉總面積平方公尺', 'en': 'Park_Sqm', 'desc': '車位移轉總面積(平方公尺)'},
    {'zh': '主建物面積占建物移轉總面積之比例', 'json': 'bs', 'xls': None, 'en': 'MainBS_rat', 'desc': '主建物面積占建物移轉總面積之比例'},
    {'zh': '主建物面積占建物移轉總面積(扣除車位面積)之比例', 'json': 'es', 'xls': None, 'en': 'MainES_rat', 'desc': '主建物面積占建物移轉總面積(扣除車位面積)之比例'},
    {'zh': '社區簡稱', 'json': 'bn', 'xls': None, 'en': 'Comm_abb', 'desc': '社區簡稱'},
    {'zh': '主建物面積', 'json': None, 'xls': '主建物面積', 'en': 'main_b_sqm', 'desc': '主建物面積'},
    {'zh': '附屬建物面積', 'json': None, 'xls': '附屬建物面積', 'en': 'aux_b_sqm', 'desc': '附屬建物面積'},
    {'zh': '陽台面積', 'json': None, 'xls': '陽台面積', 'en': 'balc_sqm', 'desc': '陽台面積'},
    {'zh': '建物型態', 'json': 'b', 'xls': '建物型態', 'en': 'Bulid_Type', 'desc': '華廈、住宅大樓...'},
    {'zh': '建築完成年月', 'json': 'g', 'xls': '建築完成年月', 'en': 'Comp_Date', 'desc': '建築完成年月'},
    {'zh': '屋齡', 'json': 'g (衍生)', 'xls': None, 'en': 'Bulid_Age', 'desc': '屋齡(年)'},
    {'zh': '總樓層數', 'json': 'f', 'xls': '總樓層數', 'en': 'Total_flr', 'desc': '建物總樓層'},
    {'zh': '移轉層次', 'json': 'f', 'xls': '移轉層次', 'en': 'Shift_flr', 'desc': '移轉層次'},
    {'zh': '主要用途', 'json': 'pu', 'xls': '主要用途', 'en': 'Main_Use', 'desc': '住家用、商業用...'},
    {'zh': '主要建材', 'json': None, 'xls': '主要建材', 'en': 'Material', 'desc': '主要建材'},
    {'zh': '建物格局', 'json': 'v', 'xls': None, 'en': 'Layout_C', 'desc': '建物格局'},
    {'zh': '建物現況格局-房', 'json': 'v (衍生)', 'xls': '建物現況格局-房', 'en': 'Layout_r', 'desc': '建物現況格局-房'},
    {'zh': '建物現況格局-廳', 'json': 'v (衍生)', 'xls': '建物現況格局-廳', 'en': 'Layout_l', 'desc': '建物現況格局-廳'},
    {'zh': '建物現況格局-衛', 'json': 'v (衍生)', 'xls': '建物現況格局-衛', 'en': 'Layout_b', 'desc': '建物現況格局-衛'},
    {'zh': '建物現況格局-隔間', 'json': None, 'xls': '建物現況格局-隔間', 'en': 'Layout_p', 'desc': '建物現況格局-隔間'},
    {'zh': '有無管理組織', 'json': 'm', 'xls': '有無管理組織', 'en': 'Bulid_Mgmt', 'desc': '有/無管理組織'},
    {'zh': '電梯', 'json': 'el', 'xls': '電梯', 'en': 'Elevator', 'desc': '有/無電梯'},
    {'zh': '車位類別', 'json': None, 'xls': '車位類別', 'en': 'Park_Type', 'desc': '車位類別'},
    {'zh': '總價元', 'json': 'tp', 'xls': '總價元', 'en': 'Land_Value', 'desc': '交易案例總價(元)'},
    {'zh': '單價元平方公尺', 'json': None, 'xls': '單價元平方公尺', 'en': 'Land_Price', 'desc': '交易案例單價(元/平方公尺)'},
    {'zh': '車位總價元', 'json': 'cp', 'xls': '車位總價元', 'en': 'Park_Value', 'desc': '交易案例車位總價(元)'},
    {'zh': '備註', 'json': 'note', 'xls': '備註', 'en': 'Trans_Note', 'desc': '交易案例備註'},
    {'zh': '編號', 'json': None, 'xls': '編號', 'en': 'Serial_No', 'desc': '不動產系統ID'},
    {'zh': '(自訂) 經度', 'json': 'lon', 'xls': None, 'en': 'Trans_lon', 'desc': '交易案例經度'},
    {'zh': '(自訂) 緯度', 'json': 'lat', 'xls': None, 'en': 'Trans_lat', 'desc': '交易案例緯度'},
    {'zh': '(自訂) 資料年度', 'json': '(衍生)', 'xls': None, 'en': 'qtr_rec', 'desc': '交易案例資料年度'},
    {'zh': '(自訂) 合併狀態', 'json': '(衍生)', 'xls': None, 'en': 'merge_status', 'desc': '標註JSON與XLSX是否成功關聯'},
]
ZH_TO_EN_MAP = {item['zh']: item['en'] for item in FIELD_DEFINITIONS}
FINAL_ZH_ORDER = [item['zh'] for item in FIELD_DEFINITIONS]
DF_FIELD_MAP = pd.DataFrame(FIELD_DEFINITIONS).rename(columns={'zh':'原始中文', 'json':'來源 JSON Key', 'xls':'來源 XLS 欄位', 'en':'最終英文欄位(SHP/XLSX)', 'desc':'欄位說明'})

# --- 輔助函式區 ---
def find_column_by_keywords(df_columns: List[str], keywords: List[str]) -> Optional[str]:
    cleaned_cols = {col.strip(): col for col in df_columns}
    for col_clean, col_orig in cleaned_cols.items():
        if all(keyword in col_clean for keyword in keywords):
            return col_orig
    return None

def format_roc_date(date_str: Any) -> str:
    if pd.isna(date_str): return ""
    cleaned_str = str(date_str).replace('/', '').strip().replace('.0', '')
    if len(cleaned_str) == 7 and cleaned_str.isdigit():
        return f"民國{cleaned_str[:3]}年{cleaned_str[3:5]}月{cleaned_str[5:7]}日"
    return str(date_str).strip()

def get_quarter_label(date_str: Any, city_name: str) -> str:
    date_str = str(date_str).replace('/', '').strip()
    if len(date_str) == 7 and date_str.isdigit():
        year, month = date_str[:3], int(date_str[3:5])
        quarter = (month - 1) // 3 + 1
        return f"{year}年第{quarter}季_{city_name}紀錄"
    return f"未知季度_{city_name}紀錄"

def clean_for_key(text: Any) -> str:
    if pd.isna(text): return ""
    cleaned = re.sub(r'[\s#]', '', to_half_width(str(text)))
    cities_pattern = r'^(臺北市|新北市|桃園市|臺中市|臺南市|高雄市|基隆市|新竹市|嘉義市|臺灣省|宜蘭縣|新竹縣|苗栗縣|彰化縣|南投縣|雲林縣|嘉義縣|屏東縣|臺東縣|花蓮縣|澎湖縣|金門縣|連江縣)'
    cleaned = re.sub(cities_pattern, '', cleaned)
    replacements = {'台': '臺', '褔': '福', '之': '-', '０':'0', '１':'1', '２':'2', '３':'3', '４':'4', '５':'5', '６':'6', '７':'7', '８':'8', '９':'9'}
    for old, new in replacements.items():
        cleaned = cleaned.replace(old, new)
    cleaned = re.sub(r'\(.*\)|（.*）', '', cleaned)
    return cleaned

def to_half_width(text: Any) -> str:
    if pd.isna(text): return ""
    return "".join([chr(ord(c) - 65248) if 65281 <= ord(c) <= 65374 else ' ' if ord(c) == 12288 else c for c in str(text)])

def _parse_layout(layout_series: pd.Series) -> Tuple[pd.Series, pd.Series, pd.Series]:
    def get_layout_part(pattern, text):
        match = re.search(pattern, str(text))
        return match.group(1) if match else None
    rooms = layout_series.apply(lambda x: get_layout_part(r'(\d+)房', x))
    living_rooms = layout_series.apply(lambda x: get_layout_part(r'(\d+)廳', x))
    bathrooms = layout_series.apply(lambda x: get_layout_part(r'(\d+)衛', x))
    return rooms, living_rooms, bathrooms

def _apply_manual_corrections(df_main: pd.DataFrame, correction_path: Path) -> Tuple[pd.DataFrame, set]:
    if not correction_path.exists():
        print("INFO: 未找到手動校正檔 (manual_correction.xlsx)，跳過此步驟。")
        return df_main, set()
    try:
        df_corr = pd.read_excel(correction_path, dtype=str)
        if 'original_composite_key' not in df_corr.columns or 'corrected_composite_key' not in df_corr.columns:
            print("警告：手動校正檔格式不符，缺少必要欄位，將不進行校正。")
            return df_main, set()
        df_corr.dropna(subset=['original_composite_key', 'corrected_composite_key'], inplace=True)
        corr_map = df_corr.set_index('original_composite_key')['corrected_composite_key'].to_dict()
        corrected_keys = set(corr_map.keys())
        def apply_corr(key):
            return corr_map.get(key, key)
        df_main['composite_key'] = df_main['original_composite_key'].apply(apply_corr)
        print(f"INFO: 成功應用 {len(df_corr)} 筆手動校正規則。")
        return df_main, corrected_keys
    except Exception as e:
        print(f"警告：讀取或應用手動校正檔時發生錯誤：{e}")
        return df_main, set()

def _generate_mismatch_report(df_unmatched_original: pd.DataFrame, df_supplement: pd.DataFrame, city_name: str, year: str, output_dir: Path):
    if df_unmatched_original.empty:
        return
    report_dir = output_dir / city_name / year / '年度總表'
    report_dir.mkdir(parents=True, exist_ok=True)
    report_path = report_dir / f'{city_name}_{year}_unmatched_records_report.xlsx'
    report_cols = {
        'original_composite_key': '產生的複合鍵(原始)', 'a_clean': '用於匹配的清理後地址',
        'e_std': '用於匹配的標準化日期', 'tp_std': '用於匹配的標準化總價', 'a': '原始JSON地址'
    }
    df_report = df_unmatched_original[list(report_cols.keys())].rename(columns=report_cols)
    if fuzz_process and not df_supplement.empty and 'composite_key' in df_supplement.columns:
        print("INFO: 正在為偵錯報告執行模糊比對，尋找最相似的 XLSX 紀錄...")
        supplement_grouped = df_supplement.groupby('town_key')
        fuzzy_matches = []
        for index, row in df_unmatched_original.iterrows():
            town_key = row.get('town_key')
            if town_key and town_key in supplement_grouped.groups:
                candidates_df = supplement_grouped.get_group(town_key)
                choices = candidates_df['composite_key'].tolist()
                query_key = row['composite_key']
                
                # v4.8.3 修正: 使用 extract 並在之後過濾分數
                top_matches = fuzz_process.extract(query_key, choices, limit=3)
                best_matches = [match for match in top_matches if match[1] >= 85]

                if best_matches:
                    suggestions = "; ".join([f"{match[0]} (分數:{match[1]})" for match in best_matches])
                    fuzzy_matches.append({
                        'original_composite_key': row['original_composite_key'],
                        '最相似的XLSX複合鍵範例': suggestions,
                    })
        if fuzzy_matches:
            df_fuzzy = pd.DataFrame(fuzzy_matches)
            # v4.8.3 修正: 明確指定 merge 的左右欄位名稱
            df_report = pd.merge(df_report, df_fuzzy, left_on='產生的複合鍵(原始)', right_on='original_composite_key', how='left').drop(columns=['original_composite_key'])
    df_report.to_excel(report_path, index=False)
    print(f"INFO: 產出 {len(df_report)} 筆無法匹配的 JSON 資料偵錯報告: {report_path}")

def _merge_supplementary_data(df_main: pd.DataFrame, df_supplement: pd.DataFrame, city_name: str, year: str, output_dir: Path, corrected_keys: set) -> pd.DataFrame:
    if df_supplement.empty:
        df_main['merge_status'] = '此筆無關聯紀錄'
        return df_main
    addr_col, date_col, price_col = [find_column_by_keywords(list(df_supplement.columns), k) for k in [["土地", "門牌"], ["交易", "年"], ["總價", "元"]]]
    if all([addr_col, date_col, price_col]):
        df_supplement['e_std'] = df_supplement[date_col].astype(str).str.replace(r'\.0$', '', regex=True).str.strip()
        df_supplement['a_clean'] = df_supplement[addr_col].apply(clean_for_key)
        df_supplement['tp_std'] = df_supplement[price_col].astype(str).str.replace(r'\.0$', '', regex=True).str.strip()
        df_supplement['composite_key'] = df_supplement['a_clean'] + '_' + df_supplement['e_std'] + '_' + df_supplement['tp_std']
        
        sup_cols_to_merge = [col for col in df_supplement.columns if col not in df_main.columns] + ['composite_key']
        df_merged = pd.merge(df_main, df_supplement[sup_cols_to_merge].drop_duplicates(subset=['composite_key']), on='composite_key', how='left', indicator=True)
        
        def assign_status(row):
            if row['original_composite_key'] in corrected_keys and row['_merge'] == 'both': return '已複查更正'
            elif row['_merge'] == 'both': return 'OK'
            else: return '此筆無關聯紀錄'
        
        df_merged['merge_status'] = df_merged.apply(assign_status, axis=1)
        df_merged = df_merged.drop(columns=['_merge'])
        unmatched_json_mask = df_merged['merge_status'] == '此筆無關聯紀錄'
        unmatched_count = unmatched_json_mask.sum()
        if unmatched_count > 0:
            print(f"警告：有 {unmatched_count} 筆 JSON 資料無法對應到 XLSX。")
            unmatched_keys = df_merged.loc[unmatched_json_mask, 'original_composite_key']
            df_unmatched_original = df_main[df_main['original_composite_key'].isin(unmatched_keys)].copy()
            _generate_mismatch_report(df_unmatched_original, df_supplement, city_name, year, output_dir)

        matched_count = (df_merged['merge_status'] != '此筆無關聯紀錄').sum()
        print(f"INFO: 成功透過索引鍵比對並合併 {matched_count} 筆 XLSX 補充資料。")
        return df_merged
    else:
        print("警告：XLSX 中缺少建立索引鍵的必要欄位 (地址/日期/總價)，將不進行合併。")
        df_main['merge_status'] = '此筆無關聯紀錄'
        return df_main

def _parse_land_info(row: pd.Series, land_codes: pd.DataFrame) -> List[Optional[str]]:
    if pd.notna(row.get('地段地籍')):
        land_address = str(row['地段地籍'])
        main_section, sub_section, land_num_str, land_section_full = None, None, None, None
        
        if '小段' in land_address:
            parts = land_address.split('小段')
            before_sub, after_sub = parts[0], parts[1]
            num_match = re.search(r'([\d-]+)地號', after_sub)
            if not num_match: return [land_address, None, None, None]
            land_num_str = num_match.group(1)
            if '段' in before_sub:
                sub_parts = before_sub.split('段')
                main_section, sub_section = sub_parts[0].strip(), sub_parts[1].strip()
            else:
                main_section, sub_section = before_sub.strip() + '小段', ''
            land_section_full = f"{main_section}{sub_section if sub_section else ''}{'小段' if '小段' not in main_section else ''}"
        elif '段' in land_address:
            parts = land_address.split('段')
            before_main, after_main = parts[0], parts[1]
            num_match = re.search(r'([\d-]+)地號', after_main)
            if not num_match: return [land_address, None, None, None]
            land_num_str = num_match.group(1)
            numeral_map = {'一':1, '二':2, '三':3, '四':4, '五':5, '六':6, '七':7, '八':8, '九':9, '十':10}
            last_char = before_main.strip()[-1] if before_main.strip() else ''
            if last_char in numeral_map:
                main_section, sub_section = before_main.strip()[:-1], last_char
            else:
                main_section, sub_section = before_main.strip(), ''
            land_section_full = f"{before_main.strip()}段"
        else:
            return [land_address, None, None, None]

        parts = land_num_str.split('-')
        std_land_num = f"{parts[0].zfill(4)}{parts[1].zfill(4) if len(parts) > 1 else '0000'}"
        office_key, office_code_val = row.get('所區碼'), row.get('事務所代碼')
        if pd.isna(office_key): return [land_section_full, std_land_num, None, None]
        query = (land_codes['所區碼'] == office_key) & (land_codes['段'] == main_section) & (land_codes['小段'] == sub_section)
        land_code_row = land_codes[query]
        if not land_code_row.empty:
            land_code = land_code_row.iloc[0]['代碼']
            full_code = f"{office_code_val}{land_code}{std_land_num}"
            return [land_section_full, std_land_num, land_code, full_code]
        else:
            return [land_section_full, std_land_num, None, None]
    return [None] * 4

def process_city_year(city_name: str, year: str, city_codes: pd.DataFrame, land_codes: pd.DataFrame, output_dir: Path):
    print(f"\n--- 開始處理 {city_name} {year} 年度資料 ---")
    json_dir, xlsx_dir = Path.cwd()/"lvr_input"/city_name/year/'JSON', Path.cwd()/"lvr_input"/city_name/year/'XLSX'

    df_main = pd.DataFrame()
    if json_dir.exists():
        files = [f for f in os.listdir(json_dir) if f.endswith(('.txt', '.json'))]
        if files: df_main = pd.DataFrame([item for f in files for item in json.load(open(json_dir/f, 'r', encoding='utf-8-sig'))])
    if df_main.empty: print(f"警告：在 {json_dir} 中未找到任何有效的 JSON 資料。"); return None
    
    df_supplement = pd.DataFrame()
    if xlsx_dir.exists():
        files = [f for f in os.listdir(xlsx_dir) if f.endswith(('.xls', '.xlsx'))]
        if files:
            try: 
                df_supplement = pd.concat([pd.read_excel(xlsx_dir/f, sheet_name='不動產買賣', engine=None) for f in files], ignore_index=True)
            except Exception as e: 
                print(f"警告：讀取 XLSX 檔案失敗：{e}")
    print(f"INFO: JSON 資料共 {len(df_main)} 筆，XLSX 資料共 {len(df_supplement)} 筆。")
    
    # 預先為 df_main 加上 town_key
    df_main_enriched = pd.merge(df_main, city_codes[['town_key', '鄉鎮市區名稱']], left_on='town', right_on='town_key', how='left')

    # 預先為 df_supplement 加上 town_key
    if not df_supplement.empty:
        town_col_xls = find_column_by_keywords(list(df_supplement.columns), ["鄉鎮市區"])
        if town_col_xls:
            df_supplement_enriched = pd.merge(df_supplement, city_codes[['鄉鎮市區名稱', 'town_key']], left_on=town_col_xls, right_on='鄉鎮市區名稱', how='left')
        else:
            df_supplement_enriched = df_supplement.copy(); df_supplement_enriched['town_key'] = None
    else:
        df_supplement_enriched = df_supplement.copy()

    df_main_enriched['a_processed'] = df_main_enriched['a'].apply(lambda x: str(x).split('#')[-1] if pd.notna(x) and '#' in str(x) else str(x))
    df_main_enriched['a_clean'] = df_main_enriched['a_processed'].apply(clean_for_key)
    df_main_enriched['e_std'] = df_main_enriched['e'].astype(str).str.replace('/', '', regex=False).str.strip()
    df_main_enriched['tp_std'] = df_main_enriched['tp'].astype(str).str.replace(',', '', regex=False).str.strip()
    df_main_enriched['original_composite_key'] = df_main_enriched['a_clean'] + '_' + df_main_enriched['e_std'] + '_' + df_main_enriched['tp_std']
    df_main_enriched['composite_key'] = df_main_enriched['original_composite_key']
    
    correction_path = Path.cwd()/"lvr_input"/city_name/year/'manual_correction.xlsx'
    df_main_enriched, corrected_keys = _apply_manual_corrections(df_main_enriched, correction_path)
    
    df_merged = _merge_supplementary_data(df_main_enriched, df_supplement_enriched, city_name, year, output_dir, corrected_keys)

    df_enriched = pd.merge(df_merged, city_codes, on='town_key', how='left', suffixes=('_merged', '_codes'))
    
    failed_count = df_enriched['鄉鎮市區名稱_codes'].isna().sum()
    if failed_count > 0:
        print(f"警告：有 {failed_count} 筆資料無法關聯到代碼表。")
        failed_mask = df_enriched['鄉鎮市區名稱_codes'].isna()
        df_enriched.loc[failed_mask, '縣市名稱'] = city_name
        df_enriched.loc[failed_mask, '鄉鎮市區名稱'] = '未知鄉鎮'

    df_final = pd.DataFrame()
    for item in FIELD_DEFINITIONS:
        zh, json_key, xls_key = item['zh'], item['json'], item['xls']
        if zh == '(自訂) 資料索引':
            df_final[zh] = df_enriched.get('original_composite_key')
            continue
        if xls_key and xls_key in df_enriched.columns:
            df_final[zh] = df_enriched[xls_key].fillna(df_enriched.get(json_key, pd.NA))
        elif json_key and json_key in df_enriched.columns:
            df_final[zh] = df_enriched[json_key]
        else:
            df_final[zh] = pd.NA
    
    df_final['(自訂) 合併狀態'] = df_enriched.get('merge_status')
    df_final['(自訂) 資料年度'] = df_enriched.apply(lambda r: get_quarter_label(r.get('e_std'), r.get('縣市名稱', city_name)), axis=1)
    df_final['交易年月日'] = df_final['交易年月日'].apply(format_roc_date)
    df_final['建築完成年月'] = df_final['建築完成年月'].apply(format_roc_date)
    df_final['屋齡'] = df_enriched.get('g')
    
    layout_series = df_final.get('建物格局')
    if layout_series is not None and not layout_series.isnull().all():
        rooms, living_rooms, bathrooms = _parse_layout(layout_series)
    else:
        rooms, living_rooms, bathrooms = pd.NA, pd.NA, pd.NA
    df_final['建物現況格局-房'], df_final['建物現況格局-廳'], df_final['建物現況格局-衛'] = rooms, living_rooms, bathrooms
    
    def format_display_name(name, code): return f"{name if pd.notna(name) else '未知'}({code if pd.notna(code) else ''})"
    df_final['(自訂) 縣市'] = df_enriched.apply(lambda r: format_display_name(r.get('縣市名稱'), r.get('city')), axis=1)
    df_final['鄉鎮市區'] = df_enriched.apply(lambda r: format_display_name(r.get('鄉鎮市區名稱_merged'), r.get('town_key')), axis=1)
    
    df_final['(自訂) 地政事務所名稱'] = df_enriched.get('事務所名稱')
    df_final['(自訂) 地政事務所代碼'] = df_enriched.get('事務所代碼')
    
    addr_split = df_enriched['a_processed'].apply(lambda x: (x, None) if pd.notna(x) and '地號' not in str(x) else (None, x))
    df_final['建物門牌'] = [clean_for_key(s[0]) for s in addr_split]
    df_final['地段地籍'] = [clean_for_key(s[1]) for s in addr_split]

    temp_df_for_land = pd.concat([df_enriched, df_final], axis=1)
    land_info = temp_df_for_land.apply(lambda row: _parse_land_info(row, land_codes), axis=1)
    df_final['(自訂) 地段名稱'], df_final['(自訂) 地號'], df_final['(自訂) 地段代碼'], df_final['(自訂) 完整地籍編號'] = zip(*land_info)

    return df_final

def save_outputs(df_result: pd.DataFrame, city_name: str, year: str, output_dir: Path):
    output_base = output_dir / city_name / year
    
    df_result = df_result.reindex(columns=FINAL_ZH_ORDER)
    df_en = df_result.rename(columns=ZH_TO_EN_MAP)
    
    output_total_dir = output_base / '年度總表'
    output_total_dir.mkdir(parents=True, exist_ok=True)
    excel_path = output_total_dir / f'{city_name}_{year}_整合總表.xlsx'
    with ExcelWriter(excel_path, engine='openpyxl') as writer:
        df_en.to_excel(writer, sheet_name='不動產買賣', index=False)
        DF_FIELD_MAP.to_excel(writer, sheet_name='欄位中英文對照表', index=False)
    print(f"已產出 {city_name} {year} 年度整合總表 Excel: {excel_path}")

    if 'Trans_lon' in df_en.columns and 'Trans_lat' in df_en.columns:
        df_shp = df_en.dropna(subset=['Trans_lon', 'Trans_lat']).copy()
        if not df_shp.empty:
            shp_path = output_total_dir / f'{city_name}_{year}_GIS總表.shp'
            gdf = geopandas.GeoDataFrame(df_shp, geometry=geopandas.points_from_xy(df_shp.Trans_lon, df_shp.Trans_lat), crs="EPSG:4326")
            gdf.to_file(shp_path, encoding='utf-8')
            print(f"已產出 {city_name} {year} 年度 GIS 總表 Shapefile: {shp_path}")

    empty_cols = [col for col in df_en.columns if df_en[col].isnull().all()]
    if empty_cols:
        print(f"\n警告：在 '{city_name} {year}' 的最終輸出中，以下欄位完全沒有資料：\n- " + "\n- ".join(empty_cols))
    
    output_partition_dir = output_base / '分區資料'
    if not df_result.empty and '鄉鎮市區' in df_result.columns:
        for (town, quarter), group in df_result.groupby(['鄉鎮市區', '(自訂) 資料年度']):
            if pd.isna(town) or not town: continue
            town_name_clean = str(town).split('(')[0]
            if not town_name_clean: continue
            
            quarter_name = str(quarter).split('_')[0]
            town_dir = output_partition_dir / town_name_clean
            town_dir.mkdir(parents=True, exist_ok=True)
            
            group_en = group.reindex(columns=FINAL_ZH_ORDER).rename(columns=ZH_TO_EN_MAP)
            
            p_excel_path = town_dir / f'{city_name}_{town_name_clean}_{quarter_name}_整合檔.xlsx'
            with ExcelWriter(p_excel_path, engine='openpyxl') as writer:
                group_en.to_excel(writer, sheet_name='不動產買賣', index=False)
                DF_FIELD_MAP.to_excel(writer, sheet_name='欄位中英文對照表', index=False)
                
            if 'Trans_lon' in group_en.columns and 'Trans_lat' in group_en.columns:
                df_shp_group = group_en.dropna(subset=['Trans_lon', 'Trans_lat']).copy()
                if not df_shp_group.empty:
                    p_shp_path = town_dir / f'{city_name}_{town_name_clean}_{quarter_name}_GIS.shp'
                    gdf_group = geopandas.GeoDataFrame(df_shp_group, geometry=geopandas.points_from_xy(df_shp_group.Trans_lon, df_shp_group.Trans_lat), crs="EPSG:4326")
                    gdf_group.to_file(p_shp_path, encoding='utf-8')
        print(f"已產出 {city_name} {year} 所有鄉鎮與季度的分區檔案。")

def main(config: dict):
    lvr_config = config.get("lvr_processing", {})
    if not lvr_config.get("enabled", False):
        print("INFO: lvr_processing 已被禁用。"); return
        
    PROJECT_ROOT = Path.cwd()
    source_dir = PROJECT_ROOT / lvr_config["source_data_dir"]
    output_dir = PROJECT_ROOT / lvr_config["processed_data_dir"]
    city_code_path = PROJECT_ROOT / lvr_config["code_tables"]["city_codes"]
    land_code_path = PROJECT_ROOT / lvr_config["code_tables"]["land_codes"]
    
    print("INFO: 正在載入與預處理代碼表...")
    try:
        df_city_codes = pd.read_csv(city_code_path, dtype=str, encoding='utf-8-sig', sep='\t')
        df_city_codes.columns = df_city_codes.columns.str.lstrip('\ufeff')
        df_city_codes = df_city_codes.apply(lambda x: x.str.strip() if x.dtype=="object" else x).fillna('')

        df_land_codes = pd.read_csv(land_code_path, dtype=str, encoding='utf-8-sig', sep=',')
        df_land_codes.columns = df_land_codes.columns.str.lstrip('\ufeff')
        df_land_codes = df_land_codes.apply(lambda x: x.str.strip() if x.dtype=="object" else x).fillna('')

    except Exception as e:
        print(f"錯誤：讀取或處理代碼表時發生錯誤：{e}"); traceback.print_exc(); sys.exit(1)

    if 'town_key' not in df_city_codes.columns or '所區碼' not in df_city_codes.columns:
        print("錯誤：代碼表 '全國縣市及鄉鎮市區代碼表.txt' 中缺少 'town_key' 或 '所區碼' 欄位。")
        sys.exit(1)

    if '所區碼' not in df_land_codes.columns:
        print(f"錯誤：在段名代碼表中找不到 '所區碼' 欄位。"); sys.exit(1)

    print(f"INFO: 開始掃描來源資料夾: {source_dir}")
    for city_name in [d for d in os.listdir(source_dir) if os.path.isdir(source_dir/d) and d!="code_tables"]:
        for year_name in os.listdir(source_dir/city_name):
            if os.path.isdir(source_dir/city_name/year_name):
                result = process_city_year(city_name, year_name, df_city_codes, df_land_codes, output_dir)
                if result is not None and not result.empty:
                    save_outputs(result, city_name, year_name, output_dir)
                else:
                    print(f"!! 處理 {city_name} {year_name} 失敗或無有效資料產出。")
    print("\n[V] 所有不動產買賣資料處理完畢。")

if __name__ == "__main__":
    print("INFO: 以獨立模式執行不動產買賣資料處理 (v4.8.3)...")
    try:
        with open("project_config.json", 'r', encoding='utf-8') as f: main(json.load(f))
    except FileNotFoundError: print("錯誤：找不到設定檔 'project_config.json'。"); sys.exit(1)
    except Exception as e: print(f"執行時發生未預期的錯誤: {e}"); traceback.print_exc(); sys.exit(1)