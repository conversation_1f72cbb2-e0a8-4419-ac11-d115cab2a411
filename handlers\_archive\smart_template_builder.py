# handlers/smart_template_builder.py (v1.0)
import pandas as pd
import shutil
from pathlib import Path
from typing import Dict

def process(base_template_path: Path, gdfs: Dict[str, pd.DataFrame], gis_db_source_key: str, output_dir: Path):
    """
    從一個基礎範本和一個 GIS 數據源，生成一個注入了最新規則的「智慧範本」。

    Args:
        base_template_path (Path): 基礎範本檔案的路徑 (e.g., ..._base.xlsx)
        gdfs (Dict[str, pd.DataFrame]): 包含所有已載入的 GeoDataFrames 的字典。
        gis_db_source_key (str): 在 gdfs 中作為後端資料庫的數據源的鍵名 (e.g., 'price_sections')。
        output_dir (Path): 智慧範本的輸出目錄。
    """
    print(f"--- 使用策略: 'smart_template_builder' v1.0 ---")
    if not base_template_path.exists():
        print(f"-> 致命錯誤: 找不到基礎範本 '{base_template_path.name}'。")
        return
    if gis_db_source_key not in gdfs:
        print(f"-> 致命錯誤: 在已載入的 GIS 來源中找不到指定的資料庫鍵名 '{gis_db_source_key}'。")
        return

    # 1. 從 gdfs 中提取資料庫 DataFrame，並移除幾何資訊
    db_gdf = gdfs[gis_db_source_key]
    db_df = pd.DataFrame(db_gdf.drop(columns='geometry', errors='ignore'))
    print(f"-> 從 GIS 來源 '{gis_db_source_key}' 成功提取 {len(db_df)} 筆評分規則。")

    # 2. 準備新的智慧範本檔案路徑
    smart_template_name = base_template_path.name.replace("_base.xlsx", "_template.xlsx")
    smart_template_path = output_dir / smart_template_name
    
    # 3. 複製基礎範本到目標位置
    shutil.copy(base_template_path, smart_template_path)
    print(f"-> 已從基礎範本複製並創建新的智慧範本: '{smart_template_name}'")

    # 4. 使用 Pandas 將規則資料寫入智慧範本的 'DB_Sheet' 工作表
    try:
        # 使用 'openpyxl' 引擎並以 'append' 模式打開，這樣可以新增工作表而不影響現有內容和公式
        with pd.ExcelWriter(smart_template_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            db_df.to_excel(writer, sheet_name='DB_Sheet', index=False)
        print(f"-> 成功將評分規則注入到 '{smart_template_name}' 的 'DB_Sheet' 工作表中。")
    except Exception as e:
        print(f"-> 錯誤: 寫入 'DB_Sheet' 時發生問題: {e}")
        # 如果寫入失敗，可以考慮刪除剛複製的檔案
        if smart_template_path.exists():
            smart_template_path.unlink()