# ===================================================================
#  檔案名稱: handlers/comparison_form_handler.py
#  版    本: v7.6 - Argument Fix
#  說    明: 最終版。修正了向 utils.write_to_cell 傳遞錯誤參數的 Bug。
# ===================================================================
import pandas as pd
import re
from openpyxl.utils import column_index_from_string
from openpyxl.utils.cell import coordinate_from_string
import logging

from .base import BaseHandler, HandlerContext
from . import utils

class ComparisonFormHandler(BaseHandler):
    
    def _write_value(self, sheet, cell_coord: str, value: any, logger: logging.Logger):
        """一個輔助函式，用於安全地解析座標並寫入儲存格。"""
        if pd.isna(cell_coord): return
        try:
            col_str, row_num = coordinate_from_string(str(cell_coord))
            col_idx = column_index_from_string(col_str)
            utils.write_to_cell(sheet, row_num, col_idx, value, logger)
        except ValueError:
            logger.warning(f"   - Mapping 警告: 發現無效的儲存格座標 '{cell_coord}'，已跳過。")

    def process(self, context: HandlerContext) -> bool:
        logger = context.logger
        logger.info(f"--- 使用策略: 'ComparisonFormHandler' v7.6 (Argument Fix) ---")
        
        group_df = context.group_df
        if group_df is None or group_df.empty:
            logger.error("此處理模式需要有效的 group_df，但未提供。"); return False
        
        current_group_id = group_df['Group_ID'].iloc[0]

        # --- 1. 讀取並驗證 Mapping 檔案 ---
        if context.mapping_path is None or not context.mapping_path.exists():
            logger.error(f"找不到 mapping 檔案: {context.mapping_path}"); return False
        try:
            s_map = pd.read_excel(context.mapping_path, sheet_name='SubjectMapping', engine='openpyxl')
            c_map = pd.read_excel(context.mapping_path, sheet_name='ComparableMapping', engine='openpyxl')

            required_s_map_cols = ['Target Cell', 'Source Field']
            if not all(col in s_map.columns for col in required_s_map_cols):
                missing = [c for c in required_s_map_cols if c not in s_map.columns]
                logger.error(f"-> Mapping 錯誤: 'SubjectMapping' 工作表中缺少欄位: {missing}"); return False
            
            required_c_map_cols = ['Source Field', 'Subject_Cell', 'Has_DiffRate']
            if not all(col in c_map.columns for col in required_c_map_cols):
                missing = [c for c in required_c_map_cols if c not in c_map.columns]
                logger.error(f"-> Mapping 錯誤: 'ComparableMapping' 工作表中缺少欄位: {missing}"); return False

        except Exception as e:
            logger.error(f"讀取 mapping 檔案 '{context.mapping_path.name}' 時發生錯誤: {e}"); return False

        # --- 2. 準備所有需要的資料 ---
        try:
            subject_row = group_df[group_df['DataType'].str.contains('比準地', na=False)].iloc[0]
            subject_source_key = str(subject_row.get('Source_Key'))
            subject_gdf = context.gdfs.get(subject_source_key)
            if subject_gdf is None: logger.error(f"在 gdfs 中找不到源 '{subject_source_key}'"); return False
            subject_id_col = str(context.gis_sources_config.get(subject_source_key, {}).get('id_column'))
            subject_id = str(subject_row['ID'])
            subject_data_rows = subject_gdf[subject_gdf[subject_id_col].astype(str) == subject_id]
            if subject_data_rows.empty: logger.error(f"在資料源 '{subject_source_key}' 中找不到比準地 ID '{subject_id}'"); return False
            subject_data = subject_data_rows.iloc[0]
            
            comparables_df = group_df[group_df['DataType'].str.contains('比較標的', na=False)].reset_index(drop=True)
            comparables_data = []
            for _, comp_row in comparables_df.iterrows():
                if pd.isna(comp_row.get('ID')) or pd.isna(comp_row.get('Source_Key')):
                    logger.warning(f"警告: 在 Group_ID '{current_group_id}' 中發現無效的比較標的資料列 (ID或Source_Key為空)，已跳過。")
                    continue
                comp_id, comp_src_key = str(comp_row['ID']), str(comp_row['Source_Key'])
                comp_gdf = context.gdfs.get(comp_src_key)
                if comp_gdf is None: logger.warning(f"找不到比較標的 ID '{comp_id}' 的資料源 '{comp_src_key}'，已跳過。"); continue
                comp_id_col = str(context.gis_sources_config.get(comp_src_key, {}).get('id_column'))
                comp_data_rows = comp_gdf[comp_gdf[comp_id_col].astype(str) == comp_id]
                if comp_data_rows.empty: logger.warning(f"在資料源 '{comp_src_key}' 中找不到 ID '{comp_id}'，已跳過。"); continue
                comparables_data.append(comp_data_rows.iloc[0])
        except Exception as e:
            logger.error(f"準備 GIS 資料時發生錯誤: {e}"); return False

        # --- 3. 處理 SubjectMapping (一次性填寫的欄位) ---
        logger.info("   - 正在處理 'SubjectMapping'...")
        for _, map_row in s_map.iterrows():
            source_field = str(map_row['Source Field'])
            value = None
            if source_field in context.project_info: value = context.project_info[source_field]
            elif source_field in subject_data: value = subject_data[source_field]
            else:
                match = re.match(r'comp(\d+)_(.*)', source_field)
                if match:
                    comp_index, actual_field = int(match.group(1)) - 1, match.group(2)
                    if 0 <= comp_index < len(comparables_data): value = comparables_data[comp_index].get(actual_field)
                    else: logger.warning(f"   - Mapping 警告: Source Field '{source_field}' 指向一個不存在的比較標的 (索引 {comp_index})")
            if value is not None:
                self._write_value(context.sheet, map_row.get('Target Cell'), value, logger)

        # --- 4. 處理 ComparableMapping (逐行比較的欄位) ---
        logger.info(f"   - 正在處理 'ComparableMapping' (共 {len(comparables_data)} 個比較標的)...")
        for _, map_rule in c_map.iterrows():
            source_field = str(map_rule['Source Field'])
            has_diff = str(map_rule.get('Has_DiffRate', 'No')).lower() in ['yes', 'y', '是']
            
            s_val = subject_data.get(source_field)
            self._write_value(context.sheet, map_rule.get('Subject_Cell'), s_val, logger)

            for i, comp_data in enumerate(comparables_data):
                comp_index = i + 1
                cell_col_name = f'Comp{comp_index}_Cell'
                diff_rate_col_name = f'Comp{comp_index}_DiffRate_Cell'

                if cell_col_name not in map_rule.index:
                    if i == 0: logger.warning(f"   - 注意: selection.xlsx 中的比較標的數量 ({len(comparables_data)}) 超過了 mapping 檔案中為 '{source_field}' 定義的欄位數量。")
                    continue
                
                c_val = comp_data.get(source_field)
                self._write_value(context.sheet, map_rule.get(cell_col_name), c_val, logger)

                if has_diff and diff_rate_col_name in map_rule.index:
                    s_numeric = pd.to_numeric(s_val, errors='coerce')
                    c_numeric = pd.to_numeric(c_val, errors='coerce')
                    diff_rate_value = "N/A"
                    if pd.notna(s_numeric) and pd.notna(c_numeric) and s_numeric != 0:
                        diff_rate_value = f"{(c_numeric - s_numeric) / s_numeric:.2%}"
                    self._write_value(context.sheet, map_rule.get(diff_rate_col_name), diff_rate_value, logger)

        logger.info(f"已成功填寫組別 {current_group_id} 的比較法資料。")
        return True