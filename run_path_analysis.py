import os
import re
import json
import sys
from dotenv import load_dotenv
from osgeo import ogr, gdal
import requests
import pandas as pd
import numpy as np
from pyproj import Transformer
import traceback

# ==============================================================================
# 輔助函數 - V14
# ==============================================================================
def setup_output_folder(path):
    """如果輸出資料夾不存在，則創建它。"""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"已創建輸出資料夾: {path}")

def transform_coords(points, src_epsg, dest_epsg):
    """批次轉換座標系統。"""
    transformer = Transformer.from_crs(src_epsg, dest_epsg, always_xy=True)
    return [transformer.transform(p[0], p[1]) for p in points]

def get_route_geojson(start_coord_wgs84, end_coord_wgs84, api_key):
    """獲取單一路線的 GeoJSON，並附加 ArcGIS Pro 偏好的 CRS 資訊。"""
    headers = {'Authorization': api_key, 'Content-Type': 'application/json'}
    body = {"coordinates": [start_coord_wgs84, end_coord_wgs84]}
    try:
        response = requests.post('https://api.openrouteservice.org/v2/directions/driving-car/geojson', json=body, headers=headers, timeout=30)
        response.raise_for_status()
        route_data = response.json()
        route_data['crs'] = {"type": "name", "properties": {"name": "EPSG:4326"}}
        return route_data
    except requests.exceptions.RequestException as e:
        print(f"      警告：無法獲取路線 GeoJSON。原因: {e}"); return None

# ==============================================================================
# 主程式邏輯 (V14 - 完全可配置平台版)
# ==============================================================================
def main():
    try:
        gdal.UseExceptions(); ogr.UseExceptions()
        
        print("--- 開始執行【V14 - 完全可配置分析平台】---")

        # 1. 讀取主設定檔
        if not os.path.exists('config.json'):
            print("錯誤：在當前目錄下找不到 'config.json' 主設定檔！"); sys.exit(1)
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        gs = config.get('general_settings', {})
        output_base_folder = gs.get('output_base_folder')
        facility_points_file_path = gs.get('facility_points_file')
        api_key_var = gs.get('api_key_env_variable', 'ORS_API_KEY')
        skip_keyword = gs.get('skip_keyword', '無')
        generate_geojson = gs.get('generate_geojson_routes', True)
        
        if not output_base_folder or not facility_points_file_path:
            print("錯誤：'config.json' 中 'output_base_folder' 或 'facility_points_file' 未設定！"); sys.exit(1)
        
        setup_output_folder(output_base_folder)

        # 2. 載入 API 金鑰
        load_dotenv()
        api_key = os.getenv(api_key_var)
        if not api_key:
            print(f"\n!!! 嚴重錯誤：找不到環境變數 '{api_key_var}'！請檢查您的 .env 檔案。"); sys.exit(1)

        # 3. 讀取設施點檔案 (支援 CSV 和 JSON)
        print(f"正在讀取設施點檔案: {facility_points_file_path}")
        if not os.path.exists(facility_points_file_path):
            print(f"錯誤：找不到指定的設施點檔案: {facility_points_file_path}"); sys.exit(1)
        
        if facility_points_file_path.lower().endswith('.csv'):
            facility_df = pd.read_csv(facility_points_file_path, encoding='utf-8-sig')
        elif facility_points_file_path.lower().endswith('.json'):
            with open(facility_points_file_path, 'r', encoding='utf-8') as f:
                facility_df = pd.DataFrame(json.load(f))
        else:
            print(f"錯誤：不支援的設施點檔案格式: {facility_points_file_path}"); sys.exit(1)
        
        # 4. 遍歷所有分析任務
        for task in config.get('analysis_tasks', []):
            task_name = task.get('task_name', '未命名任務')
            if not task.get('enabled', False):
                print(f"\n--- 任務 '{task_name}' 已禁用，跳過。 ---")
                continue

            print(f"\n--- 開始執行任務: {task_name} ---")
            
            shp_path = task.get('input_shapefile')
            fields_map = task.get('fields', {})
            output_prefix = task.get('output_filename_prefix', task_name.replace(' ', '_'))
            
            if not shp_path or not fields_map.get('land_id') or not fields_map.get('centroid_x') or not fields_map.get('centroid_y'):
                print(f"   警告：任務 '{task_name}' 設定不完整 (缺少 input_shapefile 或 fields 中的必要欄位)，跳過。"); continue

            print(f"   正在讀取目標圖層: {shp_path}")
            driver = ogr.GetDriverByName('ESRI Shapefile')
            dataSource_read = driver.Open(shp_path, 0)
            if dataSource_read is None: print(f"   警告：無法開啟 SHP 檔案 {shp_path}，跳過。"); continue
            layer_read = dataSource_read.GetLayer()
            
            feature_data_list = [{'land_id': f.GetField(fields_map['land_id']), 
                                  'Centroid_X': f.GetField(fields_map['centroid_x']), 
                                  'Centroid_Y': f.GetField(fields_map['centroid_y']), 
                                  '_internal_fid': f.GetFID()} for f in layer_read]
            report_df = pd.DataFrame(feature_data_list)
            dest_points_twd97 = [[d['Centroid_X'], d['Centroid_Y']] for d in feature_data_list]
            dest_points_wgs84 = transform_coords(dest_points_twd97, 'EPSG:3826', 'EPSG:4326')
            dataSource_read = None

            print("   正在準備 SHP 檔案結構...")
            fields_to_create = {}
            for _, sp_row in facility_df.iterrows():
                eng_cat = sp_row.get('eng_category')
                if eng_cat:
                    eng_cat_short = re.sub(r'[^a-zA-Z0-9]', '', eng_cat)[:7]
                    fields_to_create[eng_cat] = {"name_field": f"{eng_cat_short}_Na", "dist_field": f"{eng_cat_short}_D"}
            
            dataSource_update = driver.Open(shp_path, 1)
            layer_update = dataSource_update.GetLayer()
            layer_defn = layer_update.GetLayerDefn()
            existing_fields = [layer_defn.GetFieldDefn(i).GetName() for i in range(layer_defn.GetFieldCount())]
            
            for cat, field_pair in fields_to_create.items():
                if field_pair['name_field'] not in existing_fields:
                    name_field_defn = ogr.FieldDefn(field_pair['name_field'], ogr.OFTString); name_field_defn.SetWidth(50); layer_update.CreateField(name_field_defn); print(f"     已新增欄位: {field_pair['name_field']}")
                if field_pair['dist_field'] not in existing_fields:
                    dist_field_defn = ogr.FieldDefn(field_pair['dist_field'], ogr.OFTReal); dist_field_defn.SetWidth(12); dist_field_defn.SetPrecision(2); layer_update.CreateField(dist_field_defn); print(f"     已新增欄位: {field_pair['dist_field']}")
            dataSource_update = None
            
            print("   開始遍歷設施點進行計算...")
            for _, facility_row in facility_df.iterrows():
                name, eng_category = facility_row.get("name"), facility_row.get("eng_category")
                
                if name == skip_keyword:
                    print(f"     處理佔位符: {facility_row.get('category')} - {name}")
                    if eng_category and fields_to_create.get(eng_category):
                        report_df[fields_to_create[eng_category]['dist_field']] = np.nan
                    continue
                
                if not eng_category: print(f"     警告：設施點 '{name}' 缺少 'eng_category'，跳過。"); continue
                
                lon, lat = facility_row.get("lon"), facility_row.get("lat")
                twd97_x, twd97_y = facility_row.get("twd97_x"), facility_row.get("twd97_y")
                start_point_wgs84 = None
                
                if pd.notna(lon) and pd.notna(lat):
                    start_point_wgs84 = [lon, lat]
                elif pd.notna(twd97_x) and pd.notna(twd97_y):
                    start_point_wgs84 = transform_coords([[twd97_x, twd97_y]], 'EPSG:3826', 'EPSG:4326')[0]
                else:
                    print(f"     警告：設施點 '{name}' 座標缺失，跳過。"); continue
                
                print(f"     正在計算到 '{name}' 的距離...")
                try:
                    locations = [start_point_wgs84] + dest_points_wgs84
                    body = {"locations": locations, "sources": [0], "destinations": list(range(1, len(locations))), "metrics": ["distance"], "units": "m"}
                    headers = {'Authorization': api_key, 'Content-Type': 'application/json'}
                    response = requests.post('https://api.openrouteservice.org/v2/matrix/driving-car', json=body, headers=headers, timeout=90)
                    response.raise_for_status(); distances = response.json().get('distances')[0]
                except requests.exceptions.RequestException as e:
                    print(f"       錯誤：Matrix API 請求失敗，跳過此設施點。原因: {e}"); continue
                
                target_fields = fields_to_create.get(eng_category)
                if not target_fields: continue

                report_df[target_fields['name_field']] = name
                report_df[target_fields['dist_field']] = [round(d, 2) if d is not None else np.nan for d in distances]
                
                dataSource_update_loop = driver.Open(shp_path, 1)
                layer_update_loop = dataSource_update_loop.GetLayer()
                for i, data_row in enumerate(feature_data_list):
                    feature = layer_update_loop.GetFeature(data_row['_internal_fid'])
                    feature.SetField(target_fields['name_field'], name)
                    feature.SetField(target_fields['dist_field'], distances[i] if distances[i] is not None else -1)
                    layer_update_loop.SetFeature(feature)
                dataSource_update_loop = None

                if generate_geojson:
                    valid_distances = [d for d in distances if d is not None]
                    if valid_distances:
                        max_dist_index = distances.index(max(valid_distances))
                        farthest_point_wgs84 = dest_points_wgs84[max_dist_index]
                        route_geojson = get_route_geojson(start_point_wgs84, farthest_point_wgs84, api_key)
                        if route_geojson:
                            geojson_filename = os.path.join(output_base_folder, f"{output_prefix}_route_{eng_category}.geojson")
                            with open(geojson_filename, 'w', encoding='utf-8') as f: json.dump(route_geojson, f, ensure_ascii=False, indent=2)

            print(f"   正在為任務 '{task_name}' 產生最終報表...")
            report_df.drop(columns=['_internal_fid'], inplace=True, errors='ignore')
            
            report_xlsx = os.path.join(output_base_folder, f"{output_prefix}_report.xlsx")
            report_json = os.path.join(output_base_folder, f"{output_prefix}_report.json")
            
            with pd.ExcelWriter(report_xlsx, engine='openpyxl') as writer:
                report_df.to_excel(writer, sheet_name='Distance_Analysis', index=False)
                facility_df.to_excel(writer, sheet_name='Facility_Points_List', index=False)
            
            report_df.to_json(report_json, orient='records', indent=2, force_ascii=False)
            print(f"   任務 '{task_name}' 的報表已儲存。")

        print("\n--- 所有已啟用的任務執行完畢！ ---")

    except Exception:
        print("\n!!!!!!!!!!!!!!! 程式發生未預期的嚴重錯誤 !!!!!!!!!!!!!!!")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()