版面設定檔 (XLSX): 每個Excel範本將會有一個對應的 _layout.xlsx 檔案，用來定義該範本的版面結構（如區塊高度、資料起始位置等）。
欄位對應檔 (XLSX): 每個Excel範本也會有一個 _mapping.xlsx 檔案，用來定義Shapefile欄位與Excel儲存格的對應關係。


準備範本 (_template.xlsx):
確保您的 表7_宗地個別因素清冊_template.xlsx 檔案中，只有一個完整的區塊（即第一頁的版面）。
手動計算這個區塊佔了多少行（例如，從第1行到第38行），這個數字就是 template_block_height。
設定 mapping.json:
打開對應的 表7..._mapping.json 檔案。
將 template_type 改為 "register_single_sheet"。
新增或修改 "template_block_height": 38 (請使用您實際計算出的行數)。







2.版面設定檔 (_layout.xlsx) 設計
這個檔案讓使用者可以直觀地設定範本的結構。
表7_宗地個別因素清冊_layout.xlsx 範例:
Key	Value	Description
template_type	register_single_sheet	範本類型 (single_record 或 register_single_sheet)
records_per_block	7	每個版面區塊可容納的資料筆數
data_start_column	D	資料填寫的起始欄位
template_block_height	38	一個完整版面區塊佔據的總行數
static_data_sheet	靜態資料	(可選) 指向靜態資料在 mapping 檔中的工作表名稱
dynamic_data_sheet	動態資料	(可選) 指向動態資料在 mapping 檔中的工作表名稱
優點:
直觀: 使用者一看就懂。
集中管理: 所有與「版面」相關的設定都在這裡。
易於解析: 程式只需讀取這個簡單的鍵值對表格即可。


3. 欄位對應檔 (_mapping.xlsx) 設計
這是替代 mapping.json 的核心檔案，使用Excel的多個工作表(Sheet)來組織不同類型的對應。
表7_宗地個別因素清竅_mapping.xlsx 範例:
工作表1: 靜態資料
這個工作表對應 project_config.json 中的專案資訊。
Target Cell	Source Key	Description
A2	興辦事業計畫名稱	填入專案名稱
J2	案號	填入專案案號
A37	填寫日期	填入報告的填寫日期
工作表2: 動態資料
這個工作表定義 Shapefile 欄位和 Excel 範本行號的對應。
Target Row	Source Field	Description	Notes (可選)
3	SN	宗地流水號	
4	TOWN	鄉鎮市區	
5	SECTION	段小段名稱	
6	LAND_NO	地號	
7	OWNER	土地所有權人	
8	AREA	面積(M2)	
...	...	...	
29	Use_Zoning	使用分區或編定用地	