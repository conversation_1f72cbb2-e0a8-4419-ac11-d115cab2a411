# ===================================================================
#  檔案名稱: handlers/register_single_sheet_handler.py
#  版    本: v2.3 - The Keystone
#  說    明: 「欄位式」清冊處理器。
#            - [修正] 解決所有 Pylance 的類型安全警告，確保最高穩健性。
# ===================================================================
import pandas as pd
import openpyxl
from openpyxl.utils.cell import coordinate_from_string
from openpyxl.utils import get_column_letter, column_index_from_string
from pathlib import Path
from openpyxl.worksheet.worksheet import Worksheet
from typing import Union

def copy_column_format(sheet: Worksheet, source_col_idx: int, target_col_idx: int):
    source_letter = get_column_letter(source_col_idx)
    target_letter = get_column_letter(target_col_idx)
    if source_letter in sheet.column_dimensions:
        sheet.column_dimensions[target_letter].width = sheet.column_dimensions[source_letter].width
    for row_idx in range(1, sheet.max_row + 1):
        source_cell, target_cell = sheet.cell(row=row_idx, column=source_col_idx), sheet.cell(row=row_idx, column=target_col_idx)
        if source_cell.has_style:
            target_cell.font, target_cell.border, target_cell.fill, target_cell.number_format, target_cell.protection, target_cell.alignment = source_cell.font.copy(), source_cell.border.copy(), source_cell.fill.copy(), source_cell.number_format, source_cell.protection.copy(), source_cell.alignment.copy()

def process(project_info: dict, template_path: Path, layout_config: dict, layout_path: Path, mapping_path: Path, gdf: pd.DataFrame, output_dir: Path):
    print(f"--- 使用策略: 'columnar_register_handler' v2.3 ---")
    try:
        static_map_df = pd.read_excel(mapping_path, sheet_name='靜態資料對應', engine='openpyxl')
        dynamic_map_df = pd.read_excel(mapping_path, sheet_name='動態資料對應', engine='openpyxl')
    except Exception as e:
        print(f"-> 錯誤：讀取 mapping 檔案 '{mapping_path.name}' 失敗: {e}"); return

    workbook = openpyxl.load_workbook(template_path)
    active_sheet = workbook.active
    if not isinstance(active_sheet, Worksheet): print("-> 錯誤：範本中找不到有效的工作表。"); return
    sheet: Worksheet = active_sheet
    
    for _, row in static_map_df.iterrows():
        cell_coord, field = row.get('Target Cell'), row.get('Source Field')
        if pd.notna(cell_coord) and isinstance(field, str) and field in project_info:
            sheet[str(cell_coord)] = project_info.get(field)

    if 'Target Cell' not in dynamic_map_df.columns or dynamic_map_df.empty:
        print("-> 錯誤：動態資料對應表中缺少 'Target Cell' 或沒有內容。"); return
        
    template_col_str, _ = coordinate_from_string(str(dynamic_map_df['Target Cell'].iloc[0]))
    template_col_idx = column_index_from_string(template_col_str)

    # 重設索引以確保是從0開始的純數字索引
    gdf_reset = gdf.reset_index(drop=True)
    for idx, data_row in gdf_reset.iterrows():
        target_col_idx = template_col_idx + idx
        if idx > 0: copy_column_format(sheet, template_col_idx, target_col_idx)
        
        for _, map_row in dynamic_map_df.iterrows():
            source_field, template_cell_str = map_row.get('Source Field'), map_row.get('Target Cell')
            if pd.notna(template_cell_str) and isinstance(source_field, str) and source_field in data_row and pd.notna(data_row[source_field]):
                _, row_num = coordinate_from_string(str(template_cell_str))
                sheet.cell(row=row_num, column=target_col_idx).value = data_row[source_field]
    
    for _, map_row in dynamic_map_df.iterrows():
        template_cell_str = map_row.get('Target Cell')
        if pd.notna(template_cell_str): sheet[str(template_cell_str)] = None

    base_name = template_path.stem.replace('_template', '')
    output_filename = f"{base_name}_完整報告.xlsx"
    output_path = output_dir / output_filename
    workbook.save(output_path)
    print(f"-> 成功生成欄位式清冊報告: {output_filename}")