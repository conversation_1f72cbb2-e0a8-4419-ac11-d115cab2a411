# ===================================================================
#  檔案名稱: validator.py
#  功    能: 驗證指定表單的設定檔是否完整、格式是否基本正確。
# ===================================================================
import pandas as pd
from pathlib import Path
import sys
import re

def validate_form(base_name):
    print(f"--- 開始驗證表單 '{base_name}' 的設定 ---")
    base_name = re.sub(r'\s+', '_', base_name)
    templates_dir = Path("./form_input/templates")
    
    # 檢查檔案是否存在
    layout_path = templates_dir / f"{base_name}_layout.xlsx"
    mapping_path = templates_dir / f"{base_name}_mapping.xlsx"
    
    if not layout_path.exists() or not mapping_path.exists():
        print("-> 失敗：缺少 _layout.xlsx 或 _mapping.xlsx 檔案。")
        return False

    # 驗證 layout.xlsx
    try:
        df = pd.read_excel(layout_path)
        if 'Key' not in df.columns or 'Value' not in df.columns:
            print("-> 失敗：_layout.xlsx 中缺少 'Key' 或 'Value' 欄位。")
            return False
        if df[df['Key'] == 'handler_module'].empty:
            print("-> 失敗：_layout.xlsx 中必須指定 'handler_module'。")
            return False
    except Exception as e:
        print(f"-> 失敗：讀取 _layout.xlsx 時出錯: {e}")
        return False
    
    print("-> 成功：所有必要的設定檔都已找到且格式基本正確。")
    return True

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python validator.py <表單基礎名稱>")
        print("範例: python validator.py \"表4 比較法調查估價表\"")
    else:
        validate_form(sys.argv[1])