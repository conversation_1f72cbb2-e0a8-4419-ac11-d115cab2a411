# ===================================================================
#  檔案名稱: setup_tool.py
#  版    本: v1.3 - 智慧防覆蓋版
# ===================================================================
import importlib
import sys
from pathlib import Path
import re

def main():
    if len(sys.argv) < 3:
        print("用法: python setup_tool.py <處理器名稱> <表單基礎名稱> [--force]")
        print("範例: python setup_tool.py comparison_form_handler \"表4 比較法調查估價表\"")
        print("選項: --force 或 -f  強制覆蓋已存在的設定檔，不進行詢問。")
        return

    handler_name = sys.argv[1]
    base_name = re.sub(r'\s+', '_', sys.argv[2])
    force_overwrite = '--force' in sys.argv or '-f' in sys.argv
    
    output_dir = Path("./form_input/templates")
    output_dir.mkdir(parents=True, exist_ok=True)

    # v1.3 核心修正：增加覆蓋檢查
    potential_files = [f"{base_name}_layout.xlsx", f"{base_name}_mapping.xlsx"]
    existing_files = [f for f in potential_files if (output_dir / f).exists()]

    if existing_files and not force_overwrite:
        print("警告：偵測到以下設定檔已存在：")
        for f_name in existing_files:
            print(f" - {f_name}")
        response = input("您確定要覆蓋這些檔案嗎？ (yes/no): ").lower()
        if response != 'yes':
            print("操作已取消。")
            return
            
    try:
        handler_module = importlib.import_module(f"handlers.{handler_name}")
        if hasattr(handler_module, "create_config_templates"):
            handler_module.create_config_templates(base_name, output_dir)
        else:
            print(f"錯誤：處理器 '{handler_name}' 不支援自動生成設定檔。")
    except Exception as e:
        print(f"發生錯誤: {e}")

if __name__ == "__main__":
    main()